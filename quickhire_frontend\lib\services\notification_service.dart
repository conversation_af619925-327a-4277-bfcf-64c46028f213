import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import '../config/app_config.dart';

class NotificationService {
  static const String baseUrl = AppConfig.baseUrl;

  // Get authentication token
  Future<String?> _getToken() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString('token');
  }

  // Get headers with authentication
  Future<Map<String, String>> _getHeaders() async {
    final token = await _getToken();
    return {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer $token',
    };
  }

  // Update FCM token
  Future<Map<String, dynamic>> updateFcmToken(String fcmToken) async {
    try {
      final headers = await _getHeaders();
      final response = await http.put(
        Uri.parse('$baseUrl/notifications/token'),
        headers: headers,
        body: jsonEncode({
          'fcmToken': fcmToken,
        }),
      );

      final data = jsonDecode(response.body);

      if (response.statusCode == 200) {
        return {
          'success': true,
          'message': data['message'],
        };
      } else {
        return {
          'success': false,
          'message': data['message'] ?? 'Failed to update FCM token',
        };
      }
    } catch (e) {
      return {
        'success': false,
        'message': 'Network error: $e',
      };
    }
  }

  // Send test notification
  Future<Map<String, dynamic>> sendTestNotification({
    required String title,
    required String body,
    Map<String, dynamic>? data,
  }) async {
    try {
      final headers = await _getHeaders();
      final response = await http.post(
        Uri.parse('$baseUrl/notifications/test'),
        headers: headers,
        body: jsonEncode({
          'title': title,
          'body': body,
          'data': data ?? {},
        }),
      );

      final responseData = jsonDecode(response.body);

      if (response.statusCode == 200) {
        return {
          'success': true,
          'message': responseData['message'],
          'data': responseData['data'],
        };
      } else {
        return {
          'success': false,
          'message': responseData['message'] ?? 'Failed to send test notification',
        };
      }
    } catch (e) {
      return {
        'success': false,
        'message': 'Network error: $e',
      };
    }
  }

  // Initialize Firebase Cloud Messaging
  Future<void> initializeFirebaseMessaging() async {
    try {
      // Note: This is a placeholder for Firebase initialization
      // In a real implementation, you would:
      // 1. Initialize Firebase
      // 2. Request notification permissions
      // 3. Get FCM token
      // 4. Set up message handlers
      // 5. Update token on server
      
      print('Firebase messaging initialized (placeholder)');
      
      // Example of what you would do:
      /*
      FirebaseMessaging messaging = FirebaseMessaging.instance;
      
      // Request permission
      NotificationSettings settings = await messaging.requestPermission(
        alert: true,
        announcement: false,
        badge: true,
        carPlay: false,
        criticalAlert: false,
        provisional: false,
        sound: true,
      );
      
      if (settings.authorizationStatus == AuthorizationStatus.authorized) {
        print('User granted permission');
        
        // Get FCM token
        String? token = await messaging.getToken();
        if (token != null) {
          await updateFcmToken(token);
        }
        
        // Handle foreground messages
        FirebaseMessaging.onMessage.listen((RemoteMessage message) {
          print('Got a message whilst in the foreground!');
          print('Message data: ${message.data}');
          
          if (message.notification != null) {
            print('Message also contained a notification: ${message.notification}');
            // Show local notification
          }
        });
        
        // Handle background messages
        FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);
        
        // Handle notification taps
        FirebaseMessaging.onMessageOpenedApp.listen((RemoteMessage message) {
          print('A new onMessageOpenedApp event was published!');
          // Navigate to appropriate screen based on message data
        });
      }
      */
    } catch (e) {
      print('Error initializing Firebase messaging: $e');
    }
  }

  // Handle notification tap
  void handleNotificationTap(Map<String, dynamic> data) {
    try {
      final type = data['type'];
      
      switch (type) {
        case 'new_message':
          // Navigate to chat screen
          print('Navigate to chat screen');
          break;
        case 'project_match':
          // Navigate to project details
          print('Navigate to project details');
          break;
        case 'meeting_invitation':
          // Navigate to meeting details
          print('Navigate to meeting details');
          break;
        case 'meeting_reminder':
          // Navigate to meeting screen
          print('Navigate to meeting screen');
          break;
        case 'incoming_call':
          // Show incoming call screen
          print('Show incoming call screen');
          break;
        default:
          print('Unknown notification type: $type');
      }
    } catch (e) {
      print('Error handling notification tap: $e');
    }
  }

  // Show local notification (placeholder)
  Future<void> showLocalNotification({
    required String title,
    required String body,
    Map<String, dynamic>? data,
  }) async {
    try {
      // This is a placeholder for local notification
      // In a real implementation, you would use a package like flutter_local_notifications
      print('Local notification: $title - $body');
      
      /*
      const AndroidNotificationDetails androidPlatformChannelSpecifics =
          AndroidNotificationDetails(
        'quickhire_channel',
        'QuickHire Notifications',
        channelDescription: 'Notifications for QuickHire app',
        importance: Importance.max,
        priority: Priority.high,
        showWhen: false,
      );
      
      const NotificationDetails platformChannelSpecifics =
          NotificationDetails(android: androidPlatformChannelSpecifics);
      
      await flutterLocalNotificationsPlugin.show(
        0,
        title,
        body,
        platformChannelSpecifics,
        payload: jsonEncode(data ?? {}),
      );
      */
    } catch (e) {
      print('Error showing local notification: $e');
    }
  }

  // Request notification permissions (placeholder)
  Future<bool> requestNotificationPermissions() async {
    try {
      // This is a placeholder for permission request
      // In a real implementation, you would request actual permissions
      print('Requesting notification permissions (placeholder)');
      return true;
    } catch (e) {
      print('Error requesting notification permissions: $e');
      return false;
    }
  }

  // Check if notifications are enabled
  Future<bool> areNotificationsEnabled() async {
    try {
      // This is a placeholder for checking notification status
      // In a real implementation, you would check actual permission status
      print('Checking notification permissions (placeholder)');
      return true;
    } catch (e) {
      print('Error checking notification permissions: $e');
      return false;
    }
  }
}

// Background message handler (placeholder)
/*
Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  await Firebase.initializeApp();
  print("Handling a background message: ${message.messageId}");
}
*/
