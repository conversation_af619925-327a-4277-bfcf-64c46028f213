import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import '../models/job_listing.dart';
import '../config/app_config.dart';

class ProjectService {
  // Base URL for API calls
  final String baseUrl = AppConfig.projectsEndpoint;

  // Headers for API authentication
  Future<Map<String, String>> get _headers async {
    return {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer ${await _getAuthToken()}',
    };
  }

  // Get auth token from secure storage
  Future<String> _getAuthToken() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString('token') ?? '';
  }

  // Fetch all projects created by the user (employer)
  Future<List<JobListing>> fetchUserProjects() async {
    try {
      final headers = await _headers;
      final response = await http.get(
        Uri.parse('$baseUrl/employer/list'),
        headers: headers,
      ).timeout(const Duration(seconds: 10));

      if (response.statusCode == 200) {
        final List<dynamic> projectsJson = json.decode(response.body);
        return projectsJson.map((json) => JobListing.fromJson(json)).toList();
      } else if (response.statusCode == 401) {
        throw Exception('Unauthorized. Please login again.');
      } else {
        throw Exception('Failed to load projects: ${response.statusCode}');
      }
    } catch (e) {
      // For development/testing, return mock data if API fails
      print('Error fetching projects: $e. Using mock data instead.');
      return getMockProjects();
    }
  }

  // Create a new project
  Future<JobListing> createProject(JobListing project) async {
    try {
      final headers = await _headers;
      final response = await http.post(
        Uri.parse(baseUrl), // Corrected URL
        headers: headers,
        body: json.encode(project.toJson()),
      );

      if (response.statusCode == 201) {
        return JobListing.fromJson(json.decode(response.body));
      } else {
        throw Exception('Failed to create project: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error creating project: $e');
    }
  }

  // Update an existing project
  Future<JobListing> updateProject(String projectId, JobListing project) async {
    try {
      final headers = await _headers;
      final response = await http.put(
        Uri.parse('$baseUrl/$projectId'),
        headers: headers,
        body: json.encode(project.toJson()),
      );

      if (response.statusCode == 200) {
        return JobListing.fromJson(json.decode(response.body));
      } else {
        throw Exception('Failed to update project: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error updating project: $e');
    }
  }

  // Delete/cancel a project
  Future<bool> deleteProject(String projectId) async {
    try {
      final headers = await _headers;
      final response = await http.delete(
        Uri.parse('$baseUrl/$projectId'),
        headers: headers,
      );

      return response.statusCode == 204 || response.statusCode == 200;
    } catch (e) {
      throw Exception('Error deleting project: $e');
    }
  }

  // Toggle favorite/shortlist status (custom implementation)
  Future<bool> shortlistProject(String projectId, bool shortlist) async {
    try {
      final headers = await _headers;
      // Since there's no specific shortlist endpoint, we'll use the update endpoint
      final response = await http.put(
        Uri.parse('$baseUrl/$projectId'),
        headers: headers,
        body: json.encode({
          'isShortlisted': shortlist,
        }),
      );

      return response.statusCode == 200;
    } catch (e) {
      throw Exception('Error shortlisting project: $e');
    }
  }

  // For jobseekers to accept a project
  Future<bool> acceptProject(String projectId) async {
    try {
      final headers = await _headers;
      final response = await http.post(
        Uri.parse('$baseUrl/$projectId/accept'),
        headers: headers,
      );

      return response.statusCode == 200;
    } catch (e) {
      throw Exception('Error accepting project: $e');
    }
  }

  // For jobseekers to get matched projects
  Future<List<JobListing>> getMatchedProjects() async {
    try {
      final headers = await _headers;
      final response = await http.get(
        Uri.parse('$baseUrl/matches/find'),
        headers: headers,
      );

      if (response.statusCode == 200) {
        final List<dynamic> projectsJson = json.decode(response.body);
        return projectsJson.map((json) => JobListing.fromJson(json)).toList();
      } else {
        throw Exception('Failed to load matched projects: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error getting matched projects: $e');
    }
  }

  // For demonstration/testing purposes
  Future<List<JobListing>> getMockProjects() async {
    // This method returns mock data for testing when API is not available
    await Future.delayed(const Duration(seconds: 1)); // Simulate network delay

    return [
      JobListing(
        id: '2',
        employer: 'Gulfstream Aerospace',
        title: 'G-550 Vista 2024',
        description: 'Design and implement avionics systems for the next generation G-550 aircraft.',
        skills: ['Aerospace engineering', 'Electrical engineering', 'Avionics'],
        location: 'Savannah, GA',
        workType: 'onsite',
        budget: 12000.0,
        duration: '6 months',
        status: 'open',
        createdAt: DateTime.now(),
        isShortlisted: false,
      ),
      JobListing(
        id: '3',
        employer: 'Gulfstream Aerospace',
        title: 'Flight Control Systems Specialist',
        description: 'Develop and test flight control systems for G-550 aircraft upgrades.',
        skills: ['Systems engineering', 'Control systems', 'Aviation'],
        location: 'Dallas, TX',
        workType: 'onsite',
        budget: 10000.0,
        duration: '4 months',
        status: 'open',
        createdAt: DateTime.now().subtract(const Duration(days: 7)),
        isShortlisted: false,
      ),
      JobListing(
        id: '4',
        employer: 'Gulfstream Aerospace',
        title: 'Interior Design Project Manager',
        description: 'Coordinate with engineering teams and suppliers for G-550 interior design updates.',
        skills: ['Luxury interior design', 'Project management', 'Aviation'],
        location: 'Long Beach, CA',
        workType: 'hybrid',
        budget: 15000.0,
        duration: '5 months',
        status: 'open',
        createdAt: DateTime.now().subtract(const Duration(days: 14)),
        isShortlisted: false,
      ),
    ];
  }
}
