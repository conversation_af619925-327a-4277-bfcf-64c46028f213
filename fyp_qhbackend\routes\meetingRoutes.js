const express = require('express');
const { body } = require('express-validator');
const {
  createMeeting,
  getMeetings,
  getMeeting,
  updateMeeting,
  deleteMeeting,
  respondToMeeting,
  startMeeting,
  endMeeting
} = require('../controllers/meetingController');

const { protect } = require('../middlewares/auth');

const router = express.Router();

// Validation middleware for creating meetings
const createMeetingValidation = [
  body('title')
    .notEmpty()
    .withMessage('Meeting title is required')
    .isLength({ min: 3, max: 100 })
    .withMessage('Title must be between 3 and 100 characters'),
  body('scheduledTime')
    .notEmpty()
    .withMessage('Scheduled time is required')
    .isISO8601()
    .withMessage('Invalid date format'),
  body('duration')
    .optional()
    .isInt({ min: 15, max: 480 })
    .withMessage('Duration must be between 15 and 480 minutes'),
  body('meetingType')
    .optional()
    .isIn(['video', 'audio', 'in-person'])
    .withMessage('Invalid meeting type'),
  body('participants')
    .optional()
    .isArray()
    .withMessage('Participants must be an array'),
  body('participants.*.user')
    .optional()
    .isMongoId()
    .withMessage('Invalid participant user ID')
];

// Validation middleware for responding to meetings
const respondMeetingValidation = [
  body('status')
    .notEmpty()
    .withMessage('Response status is required')
    .isIn(['accepted', 'declined'])
    .withMessage('Status must be either accepted or declined')
];

// Apply authentication to all routes
router.use(protect);

// Routes
router.route('/')
  .get(getMeetings)
  .post(createMeetingValidation, createMeeting);

router.route('/:id')
  .get(getMeeting)
  .put(updateMeeting)
  .delete(deleteMeeting);

router.route('/:id/respond')
  .put(respondMeetingValidation, respondToMeeting);

router.route('/:id/start')
  .put(startMeeting);

router.route('/:id/end')
  .put(endMeeting);

module.exports = router;
