// Simple test script to verify the backend API is working
const http = require('http');

function makeRequest(options, data = null) {
  return new Promise((resolve, reject) => {
    const req = http.request(options, (res) => {
      let body = '';
      res.on('data', chunk => body += chunk);
      res.on('end', () => {
        try {
          const response = {
            statusCode: res.statusCode,
            headers: res.headers,
            body: body,
            data: body ? JSON.parse(body) : null
          };
          resolve(response);
        } catch (e) {
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            body: body,
            data: null
          });
        }
      });
    });

    req.on('error', reject);
    
    if (data) {
      req.write(JSON.stringify(data));
    }
    
    req.end();
  });
}

async function testAPI() {
  console.log('Testing QuickHire Backend API...\n');

  try {
    // Test 1: Basic health check
    console.log('1. Testing basic health check...');
    const healthCheck = await makeRequest({
      hostname: 'localhost',
      port: 5000,
      path: '/',
      method: 'GET'
    });
    console.log(`Status: ${healthCheck.statusCode}`);
    console.log(`Response: ${healthCheck.body}\n`);

    // Test 2: Get all projects (public route)
    console.log('2. Testing get all projects...');
    const projectsResponse = await makeRequest({
      hostname: 'localhost',
      port: 5000,
      path: '/api/v1/projects',
      method: 'GET'
    });
    console.log(`Status: ${projectsResponse.statusCode}`);
    console.log(`Response: ${JSON.stringify(projectsResponse.data, null, 2)}\n`);

    // Test 3: Try to create a project (should fail without auth)
    console.log('3. Testing create project without auth (should fail)...');
    const createProjectResponse = await makeRequest({
      hostname: 'localhost',
      port: 5000,
      path: '/api/v1/projects',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      }
    }, {
      title: 'Test Project',
      description: 'This is a test project',
      skills: ['JavaScript', 'Node.js'],
      location: 'Remote',
      workType: 'remote',
      budget: 1000,
      duration: '2 weeks'
    });
    console.log(`Status: ${createProjectResponse.statusCode}`);
    console.log(`Response: ${JSON.stringify(createProjectResponse.data, null, 2)}\n`);

    console.log('API tests completed!');

  } catch (error) {
    console.error('Error testing API:', error);
  }
}

testAPI();
