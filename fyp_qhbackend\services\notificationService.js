const admin = require('firebase-admin');
const User = require('../models/User');

// Initialize Firebase Admin SDK
// Note: You'll need to add your Firebase service account key
// For now, we'll create a mock implementation
let firebaseInitialized = false;

try {
  // Uncomment and configure when you have Firebase credentials
  /*
  const serviceAccount = require('../config/firebase-service-account.json');
  
  admin.initializeApp({
    credential: admin.credential.cert(serviceAccount),
    databaseURL: "https://your-project-id.firebaseio.com"
  });
  */
  
  firebaseInitialized = false; // Set to true when Firebase is configured
} catch (error) {
  console.log('Firebase not configured, using mock notifications');
  firebaseInitialized = false;
}

class NotificationService {
  
  // Send push notification to a single user
  static async sendToUser(userId, notification) {
    try {
      const user = await User.findById(userId);
      if (!user || !user.fcmToken) {
        console.log(`No FCM token found for user ${userId}`);
        return { success: false, message: 'No FCM token found' };
      }

      if (!firebaseInitialized) {
        // Mock notification for development
        console.log('Mock notification sent to user:', userId);
        console.log('Notification:', notification);
        return { success: true, message: 'Mock notification sent' };
      }

      const message = {
        token: user.fcmToken,
        notification: {
          title: notification.title,
          body: notification.body
        },
        data: notification.data || {},
        android: {
          notification: {
            icon: 'ic_notification',
            color: '#FFC107'
          }
        },
        apns: {
          payload: {
            aps: {
              badge: 1,
              sound: 'default'
            }
          }
        }
      };

      const response = await admin.messaging().send(message);
      console.log('Successfully sent message:', response);
      
      return { success: true, messageId: response };
    } catch (error) {
      console.error('Error sending notification:', error);
      return { success: false, error: error.message };
    }
  }

  // Send push notification to multiple users
  static async sendToMultipleUsers(userIds, notification) {
    try {
      const users = await User.find({ 
        _id: { $in: userIds },
        fcmToken: { $exists: true, $ne: null }
      });

      if (users.length === 0) {
        return { success: false, message: 'No users with FCM tokens found' };
      }

      if (!firebaseInitialized) {
        // Mock notification for development
        console.log('Mock notification sent to users:', userIds);
        console.log('Notification:', notification);
        return { success: true, message: 'Mock notification sent to multiple users' };
      }

      const tokens = users.map(user => user.fcmToken);
      
      const message = {
        tokens: tokens,
        notification: {
          title: notification.title,
          body: notification.body
        },
        data: notification.data || {},
        android: {
          notification: {
            icon: 'ic_notification',
            color: '#FFC107'
          }
        },
        apns: {
          payload: {
            aps: {
              badge: 1,
              sound: 'default'
            }
          }
        }
      };

      const response = await admin.messaging().sendMulticast(message);
      console.log('Successfully sent messages:', response);
      
      return { 
        success: true, 
        successCount: response.successCount,
        failureCount: response.failureCount
      };
    } catch (error) {
      console.error('Error sending notifications:', error);
      return { success: false, error: error.message };
    }
  }

  // Send notification for new message
  static async notifyNewMessage(senderId, receiverId, message) {
    try {
      const sender = await User.findById(senderId);
      
      const notification = {
        title: `New message from ${sender.name}`,
        body: message.length > 50 ? message.substring(0, 50) + '...' : message,
        data: {
          type: 'new_message',
          senderId: senderId.toString(),
          messageId: message._id?.toString() || ''
        }
      };

      return await this.sendToUser(receiverId, notification);
    } catch (error) {
      console.error('Error sending message notification:', error);
      return { success: false, error: error.message };
    }
  }

  // Send notification for project match
  static async notifyProjectMatch(employerId, jobSeekerId, projectId) {
    try {
      const employer = await User.findById(employerId);
      const jobSeeker = await User.findById(jobSeekerId);
      
      // Notify employer
      const employerNotification = {
        title: 'New Job Application',
        body: `${jobSeeker.name} applied to your project`,
        data: {
          type: 'project_match',
          projectId: projectId.toString(),
          applicantId: jobSeekerId.toString()
        }
      };

      // Notify job seeker
      const jobSeekerNotification = {
        title: 'Application Sent',
        body: `Your application has been sent to ${employer.name}`,
        data: {
          type: 'application_sent',
          projectId: projectId.toString(),
          employerId: employerId.toString()
        }
      };

      const results = await Promise.all([
        this.sendToUser(employerId, employerNotification),
        this.sendToUser(jobSeekerId, jobSeekerNotification)
      ]);

      return { success: true, results };
    } catch (error) {
      console.error('Error sending match notification:', error);
      return { success: false, error: error.message };
    }
  }

  // Send notification for meeting invitation
  static async notifyMeetingInvitation(organizerId, participantIds, meetingId, meetingTitle) {
    try {
      const organizer = await User.findById(organizerId);
      
      const notification = {
        title: 'Meeting Invitation',
        body: `${organizer.name} invited you to "${meetingTitle}"`,
        data: {
          type: 'meeting_invitation',
          meetingId: meetingId.toString(),
          organizerId: organizerId.toString()
        }
      };

      return await this.sendToMultipleUsers(participantIds, notification);
    } catch (error) {
      console.error('Error sending meeting invitation notification:', error);
      return { success: false, error: error.message };
    }
  }

  // Send notification for meeting reminder
  static async notifyMeetingReminder(meetingId, participantIds, meetingTitle, scheduledTime) {
    try {
      const timeUntilMeeting = Math.round((new Date(scheduledTime) - new Date()) / (1000 * 60));
      
      const notification = {
        title: 'Meeting Reminder',
        body: `"${meetingTitle}" starts in ${timeUntilMeeting} minutes`,
        data: {
          type: 'meeting_reminder',
          meetingId: meetingId.toString()
        }
      };

      return await this.sendToMultipleUsers(participantIds, notification);
    } catch (error) {
      console.error('Error sending meeting reminder notification:', error);
      return { success: false, error: error.message };
    }
  }

  // Update user's FCM token
  static async updateUserToken(userId, fcmToken) {
    try {
      await User.findByIdAndUpdate(userId, { fcmToken });
      return { success: true };
    } catch (error) {
      console.error('Error updating FCM token:', error);
      return { success: false, error: error.message };
    }
  }
}

module.exports = NotificationService;
