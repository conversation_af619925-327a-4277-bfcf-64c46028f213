const { v4: uuidv4 } = require('uuid');

class VideoCallService {
  constructor() {
    // In-memory storage for active calls
    // In production, you might want to use Redis or a database
    this.activeCalls = new Map();
    this.userSockets = new Map();
  }

  // Register user socket for video calling
  registerUserSocket(userId, socketId) {
    this.userSockets.set(userId, socketId);
  }

  // Unregister user socket
  unregisterUserSocket(userId) {
    this.userSockets.delete(userId);
  }

  // Get user socket ID
  getUserSocket(userId) {
    return this.userSockets.get(userId);
  }

  // Initiate a video call
  initiateCall(callerId, receiverId, callType = 'video') {
    const callId = uuidv4();
    const call = {
      id: callId,
      caller: callerId,
      receiver: receiverId,
      type: callType, // 'video' or 'audio'
      status: 'ringing',
      startTime: new Date(),
      endTime: null,
      duration: 0
    };

    this.activeCalls.set(callId, call);
    return call;
  }

  // Accept a call
  acceptCall(callId, receiverId) {
    const call = this.activeCalls.get(callId);
    if (!call || call.receiver !== receiverId) {
      return null;
    }

    call.status = 'connected';
    call.acceptTime = new Date();
    this.activeCalls.set(callId, call);
    return call;
  }

  // Reject a call
  rejectCall(callId, receiverId) {
    const call = this.activeCalls.get(callId);
    if (!call || call.receiver !== receiverId) {
      return null;
    }

    call.status = 'rejected';
    call.endTime = new Date();
    this.activeCalls.set(callId, call);
    return call;
  }

  // End a call
  endCall(callId, userId) {
    const call = this.activeCalls.get(callId);
    if (!call || (call.caller !== userId && call.receiver !== userId)) {
      return null;
    }

    call.status = 'ended';
    call.endTime = new Date();
    
    if (call.acceptTime) {
      call.duration = Math.floor((call.endTime - call.acceptTime) / 1000); // Duration in seconds
    }

    this.activeCalls.set(callId, call);
    return call;
  }

  // Get call details
  getCall(callId) {
    return this.activeCalls.get(callId);
  }

  // Get active calls for a user
  getUserActiveCalls(userId) {
    const userCalls = [];
    for (const [callId, call] of this.activeCalls) {
      if ((call.caller === userId || call.receiver === userId) && 
          ['ringing', 'connected'].includes(call.status)) {
        userCalls.push(call);
      }
    }
    return userCalls;
  }

  // Clean up old calls (should be called periodically)
  cleanupOldCalls() {
    const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);
    
    for (const [callId, call] of this.activeCalls) {
      if (call.startTime < oneHourAgo && ['ended', 'rejected'].includes(call.status)) {
        this.activeCalls.delete(callId);
      }
    }
  }

  // Handle WebRTC signaling
  handleSignaling(callId, userId, signal) {
    const call = this.activeCalls.get(callId);
    if (!call || (call.caller !== userId && call.receiver !== userId)) {
      return null;
    }

    // Determine the target user (the other participant)
    const targetUserId = call.caller === userId ? call.receiver : call.caller;
    const targetSocketId = this.getUserSocket(targetUserId);

    return {
      targetSocketId,
      signal,
      callId,
      fromUserId: userId
    };
  }

  // Generate ICE servers configuration
  getIceServers() {
    // In production, you might want to use TURN servers for better connectivity
    return [
      { urls: 'stun:stun.l.google.com:19302' },
      { urls: 'stun:stun1.l.google.com:19302' },
      // Add TURN servers if needed
      // {
      //   urls: 'turn:your-turn-server.com:3478',
      //   username: 'your-username',
      //   credential: 'your-password'
      // }
    ];
  }

  // Get call statistics
  getCallStats(callId) {
    const call = this.activeCalls.get(callId);
    if (!call) {
      return null;
    }

    const stats = {
      callId: call.id,
      duration: call.duration,
      status: call.status,
      type: call.type,
      startTime: call.startTime,
      endTime: call.endTime
    };

    return stats;
  }

  // Check if user is in a call
  isUserInCall(userId) {
    for (const [callId, call] of this.activeCalls) {
      if ((call.caller === userId || call.receiver === userId) && 
          ['ringing', 'connected'].includes(call.status)) {
        return call;
      }
    }
    return null;
  }

  // Get all calls for a user (for history)
  getUserCallHistory(userId, limit = 50) {
    const userCalls = [];
    for (const [callId, call] of this.activeCalls) {
      if (call.caller === userId || call.receiver === userId) {
        userCalls.push(call);
      }
    }
    
    // Sort by start time (most recent first)
    userCalls.sort((a, b) => new Date(b.startTime) - new Date(a.startTime));
    
    return userCalls.slice(0, limit);
  }
}

// Create a singleton instance
const videoCallService = new VideoCallService();

// Clean up old calls every hour
setInterval(() => {
  videoCallService.cleanupOldCalls();
}, 60 * 60 * 1000);

module.exports = videoCallService;
