// lib/utils/job_listing_adapter.dart

import '../models/job_listing.dart';

class JobListingAdapter {
  // Helper methods to provide backward compatibility for UI components
  
  static String getProjectName(JobListing job) {
    return job.title; // Use title as project name
  }
  
  static String getJobDescription(JobListing job) {
    return job.description; // Use description as job description
  }
  
  static String getEmploymentType(JobListing job) {
    return job.workType; // Map workType to employmentType
  }
  
  static String getExperienceLevel(JobListing job) {
    // Since we don't have experience level in the new model,
    // we can derive it from duration or return a default
    return _deriveExperienceFromDuration(job.duration);
  }
  
  static String getIndustry(JobListing job) {
    // Since we don't have industry in the new model,
    // we can derive it from skills or return a default
    return _deriveIndustryFromSkills(job.skills);
  }
  
  static DateTime getStartDate(JobListing job) {
    // Since we don't have start date, use created date + some offset
    return job.createdAt.add(const Duration(days: 7));
  }
  
  static String? getBackgroundPreferences(JobListing job) {
    // Combine skills as background preferences
    return job.skills.isNotEmpty ? job.skills.join(', ') : null;
  }
  
  static String? getCandidateLocation(JobListing job) {
    // For remote work, return "Remote", otherwise return the location
    return job.workType == 'remote' ? 'Remote' : job.location;
  }
  
  static double? getRadius(JobListing job) {
    // Default radius based on work type
    switch (job.workType) {
      case 'remote':
        return null;
      case 'onsite':
        return 25.0;
      case 'hybrid':
        return 50.0;
      default:
        return 25.0;
    }
  }
  
  static String getRelocationOptions(JobListing job) {
    // Based on work type
    return job.workType == 'remote' ? 'Yes' : 'No';
  }
  
  // Helper methods
  static String _deriveExperienceFromDuration(String duration) {
    final durationLower = duration.toLowerCase();
    if (durationLower.contains('1-2') || durationLower.contains('short')) {
      return 'Entry-level';
    } else if (durationLower.contains('3-6') || durationLower.contains('medium')) {
      return 'Mid-level';
    } else if (durationLower.contains('6+') || durationLower.contains('long')) {
      return 'Senior';
    }
    return 'Mid-level'; // Default
  }
  
  static String _deriveIndustryFromSkills(List<String> skills) {
    final skillsLower = skills.map((s) => s.toLowerCase()).toList();
    
    if (skillsLower.any((s) => s.contains('flutter') || s.contains('react') || s.contains('javascript'))) {
      return 'Technology';
    } else if (skillsLower.any((s) => s.contains('design') || s.contains('ui') || s.contains('ux'))) {
      return 'Design';
    } else if (skillsLower.any((s) => s.contains('marketing') || s.contains('seo') || s.contains('social'))) {
      return 'Marketing';
    } else if (skillsLower.any((s) => s.contains('finance') || s.contains('accounting') || s.contains('budget'))) {
      return 'Finance';
    } else if (skillsLower.any((s) => s.contains('engineering') || s.contains('mechanical') || s.contains('electrical'))) {
      return 'Engineering';
    }
    
    return 'General'; // Default
  }
  
  // Create a JobListing from form data (for create/update operations)
  static JobListing createFromFormData({
    required String title,
    required String description,
    required List<String> skills,
    required String location,
    required String workType,
    required double budget,
    required String duration,
    String status = 'open',
  }) {
    return JobListing(
      id: '',
      employer: '',
      title: title,
      description: description,
      skills: skills,
      location: location,
      workType: workType,
      budget: budget,
      duration: duration,
      status: status,
      createdAt: DateTime.now(),
    );
  }
}
