// routes/projectRoutes.js
const express = require('express');
const {
  createProject,
  getProjects,
  getProject,
  updateProject,
  getMatchedProjects,
  acceptProject,
  getProjectApplicants,
  updateApplicantStatus,
  getEmployerProjects,
  getAcceptedProjects,
  saveProject,
  getSavedProjects
} = require('../controllers/projectController');

const { protect, authorize } = require('../middlewares/auth');

const router = express.Router();

// Public routes
router.get('/', getProjects);

// Protected routes - specific routes MUST come before parameterized routes
router.post('/', protect, authorize('employer'), createProject);
router.get('/matches/find', protect, authorize('jobseeker'), getMatchedProjects);
router.get('/saved', protect, authorize('jobseeker'), getSavedProjects);
router.get('/employer/list', protect, authorize('employer'), getEmployerProjects);
router.get('/jobseeker/accepted', protect, authorize('jobseeker'), getAcceptedProjects);

// Parameterized routes come last
router.get('/:id', getProject);
router.put('/:id', protect, authorize('employer'), updateProject);
router.post('/:id/accept', protect, authorize('jobseeker'), acceptProject);
router.post('/:id/save', protect, authorize('jobseeker'), saveProject);
router.get('/:id/applicants', protect, authorize('employer'), getProjectApplicants);
router.put('/:id/applicants/:applicantId', protect, authorize('employer'), updateApplicantStatus);

module.exports = router;