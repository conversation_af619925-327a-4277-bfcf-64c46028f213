# 🌐 QuickHire Chrome Web Testing Guide

## 🚀 Quick Start for Chrome Testing

### **Step 1: Start Backend Server**

1. **Navigate to backend directory:**
   ```bash
   cd fyp_qhbackend
   ```

2. **Install dependencies (first time only):**
   ```bash
   npm install
   ```

3. **Create environment file (first time only):**
   ```bash
   # Copy the example file
   cp .env.example .env
   ```
   
   **Edit `.env` file with minimum required settings:**
   ```env
   MONGODB_URI=mongodb://localhost:27017/quickhire
   JWT_SECRET=your_super_secret_jwt_key_here
   PORT=5000
   NODE_ENV=development
   ```

4. **Start the backend server:**
   ```bash
   npm start
   ```
   
   You should see:
   ```
   🚀 Starting QuickHire Backend Server...
   Server running on port 5000
   MongoDB Connected
   ```

### **Step 2: Start Flutter Web App**

1. **Open new terminal and navigate to Flutter directory:**
   ```bash
   cd quickhire_frontend
   ```

2. **Install dependencies (first time only):**
   ```bash
   flutter pub get
   ```

3. **Run Flutter web app:**
   ```bash
   flutter run -d web
   ```
   
   Or specify the port:
   ```bash
   flutter run -d web --web-port 3000
   ```

4. **Open Chrome and navigate to:**
   ```
   http://localhost:3000
   ```
   (or whatever port Flutter shows in the terminal)

## 🔧 Configuration Details

### **Backend Configuration:**
- **Server URL**: `http://localhost:5000`
- **API Base**: `http://localhost:5000/api/v1`
- **CORS**: Configured to allow all localhost origins

### **Frontend Configuration:**
The app is already configured for localhost testing:
```dart
// In lib/config/app_config.dart
static const String serverIP = "localhost";
static const String serverPort = "5000";
static const String apiBaseUrl = "http://localhost:5000";
```

## 🧪 Testing Features in Chrome

### **1. User Registration & Login**
- Navigate to registration page
- Create employer or jobseeker account
- Check browser console for API calls
- Verify OTP (check backend console for OTP code)

### **2. Job Management**
- **Employers**: Create job listings
- **Jobseekers**: Browse and swipe through jobs
- Test the matching algorithm

### **3. Real-time Features**
- **Messaging**: Send messages between users
- **Notifications**: Test notification system
- **Meetings**: Schedule and manage meetings

### **4. API Testing**
Open Chrome DevTools (F12) and check:
- **Network tab**: See all API requests
- **Console tab**: View any JavaScript errors
- **Application tab**: Check localStorage for tokens

## 🔍 Debugging in Chrome

### **Chrome DevTools:**
1. **F12** to open DevTools
2. **Network tab**: Monitor API calls to `localhost:5000`
3. **Console tab**: Check for errors
4. **Application > Local Storage**: View stored tokens

### **Backend Logs:**
Monitor the backend terminal for:
- API request logs
- Database operations
- Error messages
- OTP codes (for email verification)

### **Common API Endpoints to Test:**
```
GET  http://localhost:5000                     - Server status
POST http://localhost:5000/api/v1/auth/register - User registration
POST http://localhost:5000/api/v1/auth/login   - User login
GET  http://localhost:5000/api/v1/projects     - Get projects
POST http://localhost:5000/api/v1/messages     - Send message
```

## 🛠️ Troubleshooting

### **Backend Issues:**
- **Port 5000 in use**: Change PORT in `.env` file
- **MongoDB connection failed**: Install MongoDB or use MongoDB Atlas
- **CORS errors**: Already configured for localhost

### **Frontend Issues:**
- **Can't connect to API**: Check if backend is running on port 5000
- **Flutter web not starting**: Run `flutter doctor` to check setup
- **Blank page**: Check browser console for errors

### **Quick Fixes:**
```bash
# If backend port is in use
echo "PORT=5001" >> .env

# If Flutter web has issues
flutter clean
flutter pub get
flutter run -d web

# Check if backend is running
curl http://localhost:5000
```

## 📊 Test Data

### **Sample Test Accounts:**
You can register any email/password combination. For testing:

**Employer Account:**
- Email: `<EMAIL>`
- Password: `password123`
- Role: Employer

**Jobseeker Account:**
- Email: `<EMAIL>`
- Password: `password123`
- Role: Jobseeker

### **Test Workflow:**
1. Register as employer → Create job listings
2. Register as jobseeker → Browse jobs
3. Test messaging between employer and jobseeker
4. Schedule meetings
5. Test notifications

## 🎯 Chrome-Specific Features

### **Local Storage:**
- Authentication tokens stored in browser
- User preferences and settings
- Chat history and cached data

### **Real-time Updates:**
- Socket.io connections for live messaging
- Real-time job updates
- Live notifications

### **Responsive Design:**
- Test on different screen sizes
- Mobile-responsive interface
- Touch-friendly controls

## 📱 Alternative Testing

If you want to test on mobile later:

**For Android Emulator:**
```dart
static const String serverIP = "********";
```

**For Physical Device:**
```dart
static const String serverIP = "YOUR_COMPUTER_IP"; // e.g., "*************"
```

## 🚀 Ready to Test!

Your QuickHire app is now configured for Chrome web testing:

1. **Backend**: `http://localhost:5000`
2. **Frontend**: `http://localhost:3000` (or Flutter's assigned port)
3. **Full API access** with proper CORS configuration
4. **Real-time features** working via Socket.io

Open Chrome, start testing, and enjoy your fully functional QuickHire app! 🎉
