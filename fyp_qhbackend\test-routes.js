// test-routes.js - Test if routes can be loaded
console.log('🧪 Testing route imports...\n');

try {
  console.log('📁 Testing authRoutes...');
  const authRoutes = require('./routes/authRoutes.js');
  console.log('✅ authRoutes loaded successfully');
} catch (error) {
  console.log('❌ authRoutes failed:', error.message);
  console.log('   Stack:', error.stack);
}

try {
  console.log('\n📁 Testing profileRoutes...');
  const profileRoutes = require('./routes/profileRoutes');
  console.log('✅ profileRoutes loaded successfully');
} catch (error) {
  console.log('❌ profileRoutes failed:', error.message);
  console.log('   Stack:', error.stack);
}

try {
  console.log('\n📁 Testing projectRoutes...');
  const projectRoutes = require('./routes/projectRoutes');
  console.log('✅ projectRoutes loaded successfully');
} catch (error) {
  console.log('❌ projectRoutes failed:', error.message);
  console.log('   Stack:', error.stack);
}

try {
  console.log('\n📁 Testing messageRoutes...');
  const messageRoutes = require('./routes/messageRoutes');
  console.log('✅ messageRoutes loaded successfully');
} catch (error) {
  console.log('❌ messageRoutes failed:', error.message);
  console.log('   Stack:', error.stack);
}

try {
  console.log('\n📁 Testing meetingRoutes...');
  const meetingRoutes = require('./routes/meetingRoutes');
  console.log('✅ meetingRoutes loaded successfully');
} catch (error) {
  console.log('❌ meetingRoutes failed:', error.message);
  console.log('   Stack:', error.stack);
}

try {
  console.log('\n📁 Testing notificationRoutes...');
  const notificationRoutes = require('./routes/notificationRoutes');
  console.log('✅ notificationRoutes loaded successfully');
} catch (error) {
  console.log('❌ notificationRoutes failed:', error.message);
  console.log('   Stack:', error.stack);
}

try {
  console.log('\n📁 Testing videoCallRoutes...');
  const videoCallRoutes = require('./routes/videoCallRoutes');
  console.log('✅ videoCallRoutes loaded successfully');
} catch (error) {
  console.log('❌ videoCallRoutes failed:', error.message);
  console.log('   Stack:', error.stack);
}

console.log('\n🎯 Route testing completed!');
