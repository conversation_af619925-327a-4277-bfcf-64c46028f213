const mongoose = require('mongoose');

const meetingSchema = new mongoose.Schema({
  title: {
    type: String,
    required: true,
    trim: true
  },
  description: {
    type: String,
    trim: true
  },
  organizer: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  participants: [{
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    status: {
      type: String,
      enum: ['pending', 'accepted', 'declined'],
      default: 'pending'
    },
    joinedAt: {
      type: Date
    }
  }],
  project: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Project'
  },
  scheduledTime: {
    type: Date,
    required: true
  },
  duration: {
    type: Number, // Duration in minutes
    default: 60
  },
  meetingType: {
    type: String,
    enum: ['video', 'audio', 'in-person'],
    default: 'video'
  },
  meetingLink: {
    type: String, // For video/audio meetings
    trim: true
  },
  location: {
    type: String, // For in-person meetings
    trim: true
  },
  status: {
    type: String,
    enum: ['scheduled', 'ongoing', 'completed', 'cancelled'],
    default: 'scheduled'
  },
  agenda: [{
    item: {
      type: String,
      required: true
    },
    duration: {
      type: Number // Duration in minutes
    }
  }],
  notes: {
    type: String,
    trim: true
  },
  recording: {
    url: String,
    duration: Number // Duration in seconds
  },
  reminders: [{
    time: {
      type: Date,
      required: true
    },
    sent: {
      type: Boolean,
      default: false
    }
  }],
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// Update the updatedAt field before saving
meetingSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

// Index for efficient queries
meetingSchema.index({ organizer: 1, scheduledTime: 1 });
meetingSchema.index({ 'participants.user': 1, scheduledTime: 1 });
meetingSchema.index({ project: 1 });
meetingSchema.index({ status: 1, scheduledTime: 1 });

module.exports = mongoose.model('Meeting', meetingSchema);
