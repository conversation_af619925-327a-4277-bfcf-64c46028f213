const express = require('express');
const { body } = require('express-validator');
const {
  initiateCall,
  acceptCall,
  rejectCall,
  endCall,
  getCall,
  getCallHistory,
  getIceServers
} = require('../controllers/videoCallController');

const { protect } = require('../middlewares/auth');

const router = express.Router();

// Validation middleware for initiating calls
const initiateCallValidation = [
  body('receiverId')
    .notEmpty()
    .withMessage('Receiver ID is required')
    .isMongoId()
    .withMessage('Invalid receiver ID'),
  body('callType')
    .optional()
    .isIn(['video', 'audio'])
    .withMessage('Call type must be either video or audio')
];

// Apply authentication to all routes
router.use(protect);

// Routes
router.route('/initiate')
  .post(initiateCallValidation, initiateCall);

router.route('/ice-servers')
  .get(getIceServers);

router.route('/history')
  .get(getCallHistory);

router.route('/:callId')
  .get(getCall);

router.route('/:callId/accept')
  .put(acceptCall);

router.route('/:callId/reject')
  .put(rejectCall);

router.route('/:callId/end')
  .put(endCall);

module.exports = router;
