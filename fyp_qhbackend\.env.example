# QuickHire Backend Environment Configuration

# Database Configuration
MONGODB_URI=mongodb://localhost:27017/quickhire
# For MongoDB Atlas, use: mongodb+srv://username:<EMAIL>/quickhire

# JWT Configuration
JWT_SECRET=your_super_secret_jwt_key_here_make_it_long_and_random

# Email Configuration (for OTP verification)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-password

# Server Configuration
PORT=5000
NODE_ENV=development

# Optional: Firebase Configuration (for push notifications)
# FIREBASE_PROJECT_ID=your-firebase-project-id
# FIREBASE_PRIVATE_KEY=your-firebase-private-key
# FIREBASE_CLIENT_EMAIL=your-firebase-client-email
