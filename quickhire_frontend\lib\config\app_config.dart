class AppConfig {
  // Server configuration for Chrome web testing
  static const String serverIP = "localhost"; // For Chrome web testing
  static const String serverPort = "5000";

  // Alternative configurations (comment/uncomment as needed):
  // static const String serverIP = "********"; // For Android emulator
  // static const String serverIP = "*************"; // For physical device (replace with your IP)

  static const String apiBaseUrl = "http://$serverIP:$serverPort";
  static const String baseUrl = "$apiBaseUrl/api/v1";
  static const String socketUrl = "http://$serverIP:$serverPort";
  
  // API endpoints
  static const String authEndpoint = "$apiBaseUrl/api/v1/auth"; // Added v1 to the path
  static const String projectsEndpoint = "$apiBaseUrl/api/v1/projects";
  
  // Auth endpoints
  static const String loginEndpoint = "$authEndpoint/login";
  static const String signupEndpoint = "$authEndpoint/register"; // Changed from signup to register
  static const String resendOtpEndpoint = "$authEndpoint/resend-otp";
  
  // Other global configuration
  static const int connectionTimeout = 30000; // 30 seconds
  static const int receiveTimeout = 30000; // 30 seconds
}


