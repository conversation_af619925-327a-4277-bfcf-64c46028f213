// lib/create_job_listing_screen.dart

import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import '../models/job_listing.dart'; // Import the proper JobListing model
import '../utils/job_listing_adapter.dart';
import '../config/app_config.dart';
import '../services/job_service.dart';

class CreateJobListingScreen extends StatefulWidget {
  static const String routeName = '/create-job-listing';
  static const String id = 'CreateJobListingScreen';

  const CreateJobListingScreen({Key? key}) : super(key: key);

  @override
  _CreateJobListingScreenState createState() => _CreateJobListingScreenState();
}

class _CreateJobListingScreenState extends State<CreateJobListingScreen> {
  // Form key for validation
  final _formKey = GlobalKey<FormState>();

  // Controllers for text fields
  final TextEditingController _projectNameController = TextEditingController();
  final TextEditingController _jobDescriptionController = TextEditingController();
  final TextEditingController _titleController = TextEditingController();
  final TextEditingController _backgroundPreferencesController = TextEditingController();
  final TextEditingController _locationController = TextEditingController();
  final TextEditingController _candidateLocationController = TextEditingController();

  // Dropdown and selection variables
  String? _selectedIndustry;
  String? _selectedEmploymentType;
  String? _selectedExperienceLevel;
  DateTime? _startDate;
  double _radius = 20;
  String _relocationOptions = 'Yes';

  // Loading state
  bool _isLoading = false;

  // API endpoint
  final String _apiEndpoint = 'https://yourbackend.com/api/createJobListing'; // Replace with your actual API URL

  final JobService _jobService = JobService();

  @override
  void initState() {
    super.initState();
    _loadSavedFormData();
  }

  @override
  void dispose() {
    // Dispose controllers to free resources
    _projectNameController.dispose();
    _jobDescriptionController.dispose();
    _titleController.dispose();
    _backgroundPreferencesController.dispose();
    _locationController.dispose();
    _candidateLocationController.dispose();
    super.dispose();
  }

  // Function to load saved form data from SharedPreferences
  Future<void> _loadSavedFormData() async {
    final prefs = await SharedPreferences.getInstance();

    setState(() {
      _projectNameController.text = prefs.getString('projectName') ?? '';
      _jobDescriptionController.text = prefs.getString('jobDescription') ?? '';
      _selectedIndustry = prefs.getString('industry');
      _selectedEmploymentType = prefs.getString('employmentType');
      _titleController.text = prefs.getString('title') ?? '';
      _selectedExperienceLevel = prefs.getString('experienceLevel');
      _backgroundPreferencesController.text = prefs.getString('backgroundPreferences') ?? '';
      String? startDateString = prefs.getString('startDate');
      if (startDateString != null) {
        _startDate = DateTime.tryParse(startDateString);
      }
      _locationController.text = prefs.getString('location') ?? '';
      _candidateLocationController.text = prefs.getString('candidateLocation') ?? '';
      _radius = prefs.getDouble('radius') ?? 20;
      _relocationOptions = prefs.getString('relocationOptions') ?? 'Yes';
    });
  }

  // Function to save form data to SharedPreferences
  Future<void> _saveFormData() async {
    final prefs = await SharedPreferences.getInstance();

    await prefs.setString('projectName', _projectNameController.text.trim());
    await prefs.setString('jobDescription', _jobDescriptionController.text.trim());
    if (_selectedIndustry != null) {
      await prefs.setString('industry', _selectedIndustry!);
    }
    if (_selectedEmploymentType != null) {
      await prefs.setString('employmentType', _selectedEmploymentType!);
    }
    await prefs.setString('title', _titleController.text.trim());
    if (_selectedExperienceLevel != null) {
      await prefs.setString('experienceLevel', _selectedExperienceLevel!);
    }
    await prefs.setString('backgroundPreferences', _backgroundPreferencesController.text.trim());
    if (_startDate != null) {
      await prefs.setString('startDate', _startDate!.toIso8601String());
    }
    await prefs.setString('location', _locationController.text.trim());
    await prefs.setString('candidateLocation', _candidateLocationController.text.trim());
    await prefs.setDouble('radius', _radius);
    await prefs.setString('relocationOptions', _relocationOptions);
  }

  // Function to clear saved form data from SharedPreferences
  Future<void> _clearFormData() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('projectName');
    await prefs.remove('jobDescription');
    await prefs.remove('industry');
    await prefs.remove('employmentType');
    await prefs.remove('title');
    await prefs.remove('experienceLevel');
    await prefs.remove('backgroundPreferences');
    await prefs.remove('startDate');
    await prefs.remove('location');
    await prefs.remove('candidateLocation');
    await prefs.remove('radius');
    await prefs.remove('relocationOptions');
  }

  // Function to handle form submission
  Future<void> _handleSubmit() async {
    if (_formKey.currentState!.validate() && _startDate != null) {
      // All validations passed
      setState(() {
        _isLoading = true;
      });

      // Save current form data
      await _saveFormData();

      // Construct the JobListing object using the adapter
      JobListing jobListing = JobListingAdapter.createFromFormData(
        title: _projectNameController.text.trim(),
        description: _jobDescriptionController.text.trim(),
        skills: _backgroundPreferencesController.text.trim().split(',').map((s) => s.trim()).where((s) => s.isNotEmpty).toList(),
        location: _locationController.text.trim(),
        workType: _selectedEmploymentType?.toLowerCase() == 'remote' ? 'remote' :
                  _selectedEmploymentType?.toLowerCase() == 'hybrid' ? 'hybrid' : 'onsite',
        budget: 5000.0, // Default budget - you can add a budget field to the form
        duration: '3 months', // Default duration - you can add a duration field to the form
        status: 'open',
      );

      try {
        // Use the consolidated service to create the job listing
        final createdJob = await _jobService.createJobListing(jobListing);
        
        // Success
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Job listing created successfully!')),
        );

        // Clear saved form data
        await _clearFormData();

        // Navigate back to dashboard or another appropriate screen
        Navigator.popUntil(context, ModalRoute.withName('/emp-dashboard'));
      } catch (e) {
        // Network or other errors
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('An error occurred: $e')),
        );
      } finally {
        // Reset loading state
        setState(() {
          _isLoading = false;
        });
      }
    } else {
      // If start date is not selected
      if (_startDate == null) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Please select a start date')),
        );
      }
    }
  }

  // Function to select start date using DatePicker
  Future<void> _selectStartDateDialog(BuildContext context) async {
    final now = DateTime.now();
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _startDate ?? now,
      firstDate: DateTime(2020),
      lastDate: DateTime(2100),
    );
    if (picked != null && picked != _startDate) {
      setState(() {
        _startDate = picked;
      });
      await _saveFormData(); // Save updated date
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Create Job Listing'),
          backgroundColor: Colors.amber,
      ),
      body: SafeArea(
        minimum: const EdgeInsets.symmetric(horizontal: 30, vertical: 8),
        child: SingleChildScrollView(
          child: Form(
            key: _formKey, // Assign the form key
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Project Name Field
                TextFormField(
                  controller: _projectNameController,
                  decoration: const InputDecoration(
                    labelText: 'Project Name',
                    hintText: 'Type Here',
                    border: OutlineInputBorder(),
                  ),
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return 'Project Name is required';
                    }
                    return null;
                  },
                  onChanged: (value) {
                    _saveFormData(); // Save data on change
                  },
                ),
                const SizedBox(height: 16),

                // Job Description Field
                TextFormField(
                  controller: _jobDescriptionController,
                  decoration: const InputDecoration(
                    labelText: 'Job Description',
                    hintText: 'Type Here',
                    border: OutlineInputBorder(),
                  ),
                  maxLines: 3,
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return 'Job Description is required';
                    }
                    return null;
                  },
                  onChanged: (value) {
                    _saveFormData(); // Save data on change
                  },
                ),
                const SizedBox(height: 24),

                // Industry Dropdown
                DropdownButtonFormField<String>(
                  decoration: const InputDecoration(
                    labelText: 'Industry',
                    border: OutlineInputBorder(),
                  ),
                  value: _selectedIndustry,
                  items: ['Tech', 'Healthcare', 'Education', 'Finance', 'Other'].map((String value) {
                    return DropdownMenuItem<String>(
                      value: value,
                      child: Text(value),
                    );
                  }).toList(),
                  onChanged: (newValue) async {
                    setState(() {
                      _selectedIndustry = newValue;
                    });
                    await _saveFormData(); // Save data on change
                  },
                  validator: (value) => value == null ? 'Please select an industry' : null,
                ),
                const SizedBox(height: 16),

                // Employment Type Dropdown
                DropdownButtonFormField<String>(
                  decoration: const InputDecoration(
                    labelText: 'Employment Type',
                    border: OutlineInputBorder(),
                  ),
                  value: _selectedEmploymentType,
                  items: ['full-time', 'part-time', 'contract', 'freelance'].map((String value) {
                    // Capitalize the first letter for better UI
                    String displayValue = value.split('-').map((word) => word[0].toUpperCase() + word.substring(1)).join(' ');
                    return DropdownMenuItem<String>(
                      value: value,
                      child: Text(displayValue),
                    );
                  }).toList(),
                  onChanged: (newValue) async {
                    setState(() {
                      _selectedEmploymentType = newValue;
                    });
                    await _saveFormData(); // Save data on change
                  },
                  validator: (value) => value == null ? 'Please select employment type' : null,
                ),
                const SizedBox(height: 16),

                // Job Title Field
                TextFormField(
                  controller: _titleController,
                  decoration: const InputDecoration(
                    labelText: 'Job Title',
                    hintText: 'Enter Job Title',
                    border: OutlineInputBorder(),
                  ),
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return 'Job Title is required';
                    }
                    return null;
                  },
                  onChanged: (value) {
                    _saveFormData(); // Save data on change
                  },
                ),
                const SizedBox(height: 16),

                // Experience Level Dropdown
                DropdownButtonFormField<String>(
                  decoration: const InputDecoration(
                    labelText: 'Experience Level',
                    border: OutlineInputBorder(),
                  ),
                  value: _selectedExperienceLevel,
                  items: ['entry', 'intermediate', 'senior', 'executive'].map((String value) {
                    String displayValue = value[0].toUpperCase() + value.substring(1);
                    return DropdownMenuItem<String>(
                      value: value,
                      child: Text(displayValue),
                    );
                  }).toList(),
                  onChanged: (newValue) async {
                    setState(() {
                      _selectedExperienceLevel = newValue;
                    });
                    await _saveFormData(); // Save data on change
                  },
                  validator: (value) => value == null ? 'Please select experience level' : null,
                ),
                const SizedBox(height: 16),

                // Background Preferences Field (Optional)
                TextFormField(
                  controller: _backgroundPreferencesController,
                  decoration: const InputDecoration(
                    labelText: 'Background Preferences (Optional)',
                    hintText: 'e.g., Hospitality, Retail',
                    border: OutlineInputBorder(),
                  ),
                  onChanged: (value) {
                    _saveFormData(); // Save data on change
                  },
                ),
                const SizedBox(height: 16),

                // Start Date Picker
                InkWell(
                  onTap: () => _selectStartDateDialog(context),
                  child: InputDecorator(
                    decoration: const InputDecoration(
                      labelText: 'Start Date',
                      border: OutlineInputBorder(),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          _startDate == null
                              ? 'mm/dd/yyyy'
                              : '${_startDate!.month}/${_startDate!.day}/${_startDate!.year}',
                          style: TextStyle(
                            color: _startDate == null ? Colors.grey.shade600 : Colors.black,
                          ),
                        ),
                        const Icon(Icons.calendar_today),
                      ],
                    ),
                  ),
                ),
                if (_startDate == null)
                  Padding(
                    padding: const EdgeInsets.only(top: 8.0, left: 12.0),
                    child: Text(
                      'Start Date is required',
                      style: TextStyle(color: Colors.red.shade700, fontSize: 12),
                    ),
                  ),
                const SizedBox(height: 16),

                // Job Location Field
                TextFormField(
                  controller: _locationController,
                  decoration: const InputDecoration(
                    labelText: 'Job Location',
                    hintText: 'Enter Job Location',
                    border: OutlineInputBorder(),
                  ),
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return 'Job Location is required';
                    }
                    return null;
                  },
                  onChanged: (value) {
                    _saveFormData(); // Save data on change
                  },
                ),
                const SizedBox(height: 16),

                // Candidate Location Field (Optional)
                TextFormField(
                  controller: _candidateLocationController,
                  decoration: const InputDecoration(
                    labelText: 'Candidate Location (Optional)',
                    hintText: 'Enter Candidate Location',
                    border: OutlineInputBorder(),
                  ),
                  onChanged: (value) {
                    _saveFormData(); // Save data on change
                  },
                ),
                const SizedBox(height: 16),

                // Radius Slider
                Text(
                  'Select a Radius: ${_radius.round()} miles',
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    color: Colors.black,
                  ),
                ),
                Slider(
                  min: 0,
                  max: 100,
                  divisions: 20,
                  value: _radius,
                  label: '${_radius.round()} miles',
                  activeColor: Colors.amber,
                  onChanged: (double value) async {
                    setState(() {
                      _radius = value;
                    });
                    await _saveFormData(); // Save data on change
                  },
                ),
                const SizedBox(height: 16),

                // Relocation Options Radio Buttons
                const Text(
                  'Relocation Options',
                  style: TextStyle(fontWeight: FontWeight.bold, color: Colors.black),
                ),
                Column(
                  children: [
                    RadioListTile<String>(
                      title: const Text('Open to relocate'),
                      value: 'Yes',
                      groupValue: _relocationOptions,
                      onChanged: (value) async {
                        setState(() {
                          _relocationOptions = value!;
                        });
                        await _saveFormData(); // Save data on change
                      },
                    ),
                    RadioListTile<String>(
                      title: const Text('Not open to relocate'),
                      value: 'No',
                      groupValue: _relocationOptions,
                      onChanged: (value) async {
                        setState(() {
                          _relocationOptions = value!;
                        });
                        await _saveFormData(); // Save data on change
                      },
                    ),
                  ],
                ),
                const SizedBox(height: 24),

                // Submit Button
                SizedBox(
                  width: double.infinity,
                  height: 50,
                  child: ElevatedButton(
                    onPressed: _isLoading ? null : _handleSubmit,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.amber, // Changed to match kGoldColor
                      shape: const StadiumBorder(),
                    ),
                    child: _isLoading
                        ? const CircularProgressIndicator(
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    )
                        : const Text(
                      'Submit',
                      style: TextStyle(color: Colors.white, fontSize: 16),
                    ),
                  ),
                ),
                const SizedBox(height: 24),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

// Rename the class to avoid conflict with the imported JobListing
class JobListingDTO {
  final String id;
  final String employer;
  final String projectName;
  final String jobDescription;
  final String industry;
  final String employmentType;
  final String title;
  final String experienceLevel;
  final String? backgroundPreferences;
  final DateTime startDate;
  final String location;
  final String? candidateLocation;
  final double? radius;
  final String relocationOptions;
  final bool isShortlisted;
  final DateTime createdAt;

  JobListingDTO({
    required this.id,
    required this.employer,
    required this.projectName,
    required this.jobDescription,
    required this.industry,
    required this.employmentType,
    required this.title,
    required this.experienceLevel,
    this.backgroundPreferences,
    required this.startDate,
    required this.location,
    this.candidateLocation,
    this.radius,
    required this.relocationOptions,
    required this.isShortlisted,
    required this.createdAt,
  });

  // Convert JobListingDTO to JSON for API
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'employer': employer,
      'projectName': projectName,
      'jobDescription': jobDescription,
      'industry': industry,
      'employmentType': employmentType,
      'title': title,
      'experienceLevel': experienceLevel,
      'backgroundPreferences': backgroundPreferences,
      'startDate': startDate.toIso8601String(),
      'location': location,
      'candidateLocation': candidateLocation,
      'radius': radius,
      'relocationOptions': relocationOptions,
      'isShortlisted': isShortlisted,
      'createdAt': createdAt.toIso8601String(),
    };
  }
}
