const videoCallService = require('../services/videoCallService');
const User = require('../models/User');
const NotificationService = require('../services/notificationService');

// @desc    Initiate a video call
// @route   POST /api/v1/calls/initiate
// @access  Private
exports.initiateCall = async (req, res) => {
  try {
    const { receiverId, callType = 'video' } = req.body;

    if (!receiverId) {
      return res.status(400).json({
        success: false,
        message: 'Receiver ID is required'
      });
    }

    // Check if receiver exists
    const receiver = await User.findById(receiverId);
    if (!receiver) {
      return res.status(404).json({
        success: false,
        message: 'Receiver not found'
      });
    }

    // Check if caller is already in a call
    const existingCall = videoCallService.isUserInCall(req.user.id);
    if (existingCall) {
      return res.status(400).json({
        success: false,
        message: 'You are already in a call'
      });
    }

    // Check if receiver is already in a call
    const receiverInCall = videoCallService.isUserInCall(receiverId);
    if (receiverInCall) {
      return res.status(400).json({
        success: false,
        message: 'Receiver is already in a call'
      });
    }

    // Initiate the call
    const call = videoCallService.initiateCall(req.user.id, receiverId, callType);

    // Get receiver's socket and emit call invitation
    const receiverSocketId = videoCallService.getUserSocket(receiverId);
    const io = req.app.get('io');
    
    if (io && receiverSocketId) {
      io.to(receiverSocketId).emit('incoming_call', {
        callId: call.id,
        caller: {
          id: req.user.id,
          name: req.user.name
        },
        type: callType
      });
    }

    // Send push notification
    await NotificationService.sendToUser(receiverId, {
      title: 'Incoming Call',
      body: `${req.user.name} is calling you`,
      data: {
        type: 'incoming_call',
        callId: call.id,
        callType: callType
      }
    });

    res.status(201).json({
      success: true,
      data: {
        callId: call.id,
        status: call.status,
        iceServers: videoCallService.getIceServers()
      }
    });
  } catch (error) {
    console.error('Error initiating call:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Accept a video call
// @route   PUT /api/v1/calls/:callId/accept
// @access  Private
exports.acceptCall = async (req, res) => {
  try {
    const { callId } = req.params;

    const call = videoCallService.acceptCall(callId, req.user.id);
    if (!call) {
      return res.status(404).json({
        success: false,
        message: 'Call not found or unauthorized'
      });
    }

    // Notify the caller
    const callerSocketId = videoCallService.getUserSocket(call.caller);
    const io = req.app.get('io');
    
    if (io && callerSocketId) {
      io.to(callerSocketId).emit('call_accepted', {
        callId: call.id,
        acceptedBy: {
          id: req.user.id,
          name: req.user.name
        }
      });
    }

    res.status(200).json({
      success: true,
      data: {
        callId: call.id,
        status: call.status,
        iceServers: videoCallService.getIceServers()
      }
    });
  } catch (error) {
    console.error('Error accepting call:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Reject a video call
// @route   PUT /api/v1/calls/:callId/reject
// @access  Private
exports.rejectCall = async (req, res) => {
  try {
    const { callId } = req.params;

    const call = videoCallService.rejectCall(callId, req.user.id);
    if (!call) {
      return res.status(404).json({
        success: false,
        message: 'Call not found or unauthorized'
      });
    }

    // Notify the caller
    const callerSocketId = videoCallService.getUserSocket(call.caller);
    const io = req.app.get('io');
    
    if (io && callerSocketId) {
      io.to(callerSocketId).emit('call_rejected', {
        callId: call.id,
        rejectedBy: {
          id: req.user.id,
          name: req.user.name
        }
      });
    }

    res.status(200).json({
      success: true,
      data: {
        callId: call.id,
        status: call.status
      }
    });
  } catch (error) {
    console.error('Error rejecting call:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    End a video call
// @route   PUT /api/v1/calls/:callId/end
// @access  Private
exports.endCall = async (req, res) => {
  try {
    const { callId } = req.params;

    const call = videoCallService.endCall(callId, req.user.id);
    if (!call) {
      return res.status(404).json({
        success: false,
        message: 'Call not found or unauthorized'
      });
    }

    // Notify the other participant
    const otherUserId = call.caller === req.user.id ? call.receiver : call.caller;
    const otherUserSocketId = videoCallService.getUserSocket(otherUserId);
    const io = req.app.get('io');
    
    if (io && otherUserSocketId) {
      io.to(otherUserSocketId).emit('call_ended', {
        callId: call.id,
        endedBy: {
          id: req.user.id,
          name: req.user.name
        },
        duration: call.duration
      });
    }

    res.status(200).json({
      success: true,
      data: {
        callId: call.id,
        status: call.status,
        duration: call.duration
      }
    });
  } catch (error) {
    console.error('Error ending call:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Get call details
// @route   GET /api/v1/calls/:callId
// @access  Private
exports.getCall = async (req, res) => {
  try {
    const { callId } = req.params;

    const call = videoCallService.getCall(callId);
    if (!call) {
      return res.status(404).json({
        success: false,
        message: 'Call not found'
      });
    }

    // Check if user has access to this call
    if (call.caller !== req.user.id && call.receiver !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: 'Unauthorized to access this call'
      });
    }

    res.status(200).json({
      success: true,
      data: call
    });
  } catch (error) {
    console.error('Error getting call:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Get user's call history
// @route   GET /api/v1/calls/history
// @access  Private
exports.getCallHistory = async (req, res) => {
  try {
    const { limit = 50 } = req.query;

    const calls = videoCallService.getUserCallHistory(req.user.id, parseInt(limit));

    res.status(200).json({
      success: true,
      count: calls.length,
      data: calls
    });
  } catch (error) {
    console.error('Error getting call history:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Get ICE servers configuration
// @route   GET /api/v1/calls/ice-servers
// @access  Private
exports.getIceServers = async (req, res) => {
  try {
    const iceServers = videoCallService.getIceServers();

    res.status(200).json({
      success: true,
      data: { iceServers }
    });
  } catch (error) {
    console.error('Error getting ICE servers:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};
