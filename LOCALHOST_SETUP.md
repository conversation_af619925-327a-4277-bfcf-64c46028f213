# 🚀 QuickHire Localhost Testing Setup Guide

## 📋 Prerequisites

Make sure you have the following installed:
- **Node.js** (v14 or higher)
- **MongoDB** (running locally or MongoDB Atlas)
- **Flutter** (latest stable version)
- **Android Studio** (for Android emulator) or **Xcode** (for iOS simulator)

## 🔧 Backend Setup

### 1. Navigate to Backend Directory
```bash
cd fyp_qhbackend
```

### 2. Install Dependencies
```bash
npm install
```

### 3. Environment Configuration
Create a `.env` file in the backend root directory:
```env
# Database
MONGODB_URI=mongodb://localhost:27017/quickhire
# Or use MongoDB Atlas: mongodb+srv://username:<EMAIL>/quickhire

# JWT Secret
JWT_SECRET=your_super_secret_jwt_key_here

# Email Configuration (for OTP)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-password

# Server Configuration
PORT=5000
NODE_ENV=development
```

### 4. Start MongoDB (if using local MongoDB)
```bash
# Windows
mongod

# macOS (with Homebrew)
brew services start mongodb-community

# Linux
sudo systemctl start mongod
```

### 5. Get Your IP Address
```bash
npm run get-ip
```
This will show your local IP address for Flutter configuration.

### 6. Start the Backend Server
```bash
# Development mode with auto-restart
npm run dev

# Or production mode
npm start
```

The server will start on `http://localhost:5000` and show network information.

## 📱 Flutter Setup

### 1. Navigate to Flutter Directory
```bash
cd quickhire_frontend
```

### 2. Install Dependencies
```bash
flutter pub get
```

### 3. Configure Server IP

Edit `lib/config/app_config.dart` and choose the appropriate configuration:

```dart
class AppConfig {
  // Choose ONE of the following based on your testing setup:
  
  // For Android Emulator:
  static const String serverIP = "********";
  
  // For iOS Simulator or Web:
  // static const String serverIP = "localhost";
  
  // For Physical Device (replace with your actual IP from step 5 above):
  // static const String serverIP = "*************";
  
  static const String apiBaseUrl = "http://$serverIP:5000";
  static const String baseUrl = "$apiBaseUrl/api/v1";
  static const String socketUrl = "http://$serverIP:5000";
  
  // ... rest of the config
}
```

### 4. Run the Flutter App

#### For Android Emulator:
```bash
flutter run
```

#### For iOS Simulator:
```bash
flutter run -d ios
```

#### For Web:
```bash
flutter run -d web
```

#### For Physical Device:
1. Enable USB debugging on your device
2. Connect via USB
3. Update `serverIP` in `app_config.dart` with your computer's IP address
4. Run: `flutter run`

## 🧪 Testing the Setup

### 1. Test Backend API
Open your browser and visit:
- `http://localhost:5000` - Should show "QuickHire API is running"
- `http://localhost:5000/api/v1/auth` - Should show route not found (expected)

### 2. Test Flutter App
1. Launch the app
2. Try registering a new account
3. Check the backend console for API requests
4. Verify OTP email functionality

## 🔍 Troubleshooting

### Backend Issues:
- **Port 5000 in use**: Change PORT in `.env` file
- **MongoDB connection failed**: Check MongoDB is running
- **Email not sending**: Verify email credentials in `.env`

### Flutter Issues:
- **Network error**: Check `serverIP` in `app_config.dart`
- **Android emulator can't connect**: Use `********` as serverIP
- **Physical device can't connect**: Use your computer's IP address

### Common Solutions:
1. **Firewall blocking**: Allow port 5000 through firewall
2. **Wrong IP address**: Run `npm run get-ip` to get correct IP
3. **CORS issues**: Backend already configured for CORS

## 📊 Available API Endpoints

Once running, your API will have these endpoints:

### Authentication
- `POST /api/v1/auth/register` - User registration
- `POST /api/v1/auth/login` - User login
- `POST /api/v1/auth/verify-email` - Email verification
- `GET /api/v1/auth/me` - Get current user

### Projects/Jobs
- `GET /api/v1/projects` - Get all projects
- `POST /api/v1/projects` - Create project (employer only)
- `GET /api/v1/projects/matches/find` - Get matched projects (jobseeker)

### Messages
- `GET /api/v1/messages` - Get conversations
- `POST /api/v1/messages` - Send message

### Meetings
- `GET /api/v1/meetings` - Get meetings
- `POST /api/v1/meetings` - Create meeting

### Video Calls
- `POST /api/v1/calls/initiate` - Start video call
- `PUT /api/v1/calls/:id/accept` - Accept call

### Notifications
- `PUT /api/v1/notifications/token` - Update FCM token
- `POST /api/v1/notifications/test` - Send test notification

## 🎯 Next Steps

1. **Test all features** using the Flutter app
2. **Check real-time features** like messaging and calls
3. **Monitor backend logs** for any errors
4. **Test on different devices** (emulator, simulator, physical device)

## 📞 Support

If you encounter any issues:
1. Check the console logs (both Flutter and Node.js)
2. Verify network connectivity
3. Ensure all dependencies are installed
4. Check firewall and antivirus settings

Happy testing! 🚀
