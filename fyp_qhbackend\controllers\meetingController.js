const Meeting = require('../models/Meeting');
const User = require('../models/User');
const Project = require('../models/Project');
const { validationResult } = require('express-validator');

// @desc    Create a new meeting
// @route   POST /api/v1/meetings
// @access  Private
exports.createMeeting = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation errors',
        errors: errors.array()
      });
    }

    const {
      title,
      description,
      participants,
      project,
      scheduledTime,
      duration,
      meetingType,
      meetingLink,
      location,
      agenda
    } = req.body;

    // Validate scheduled time is in the future
    if (new Date(scheduledTime) <= new Date()) {
      return res.status(400).json({
        success: false,
        message: 'Meeting must be scheduled for a future time'
      });
    }

    // Validate participants exist
    if (participants && participants.length > 0) {
      const participantIds = participants.map(p => p.user || p);
      const existingUsers = await User.find({ _id: { $in: participantIds } });
      if (existingUsers.length !== participantIds.length) {
        return res.status(400).json({
          success: false,
          message: 'One or more participants not found'
        });
      }
    }

    // Validate project exists if provided
    if (project) {
      const existingProject = await Project.findById(project);
      if (!existingProject) {
        return res.status(400).json({
          success: false,
          message: 'Project not found'
        });
      }
    }

    const meeting = await Meeting.create({
      title,
      description,
      organizer: req.user.id,
      participants: participants ? participants.map(p => ({
        user: p.user || p,
        status: 'pending'
      })) : [],
      project,
      scheduledTime,
      duration: duration || 60,
      meetingType: meetingType || 'video',
      meetingLink,
      location,
      agenda: agenda || []
    });

    await meeting.populate([
      { path: 'organizer', select: 'name email' },
      { path: 'participants.user', select: 'name email' },
      { path: 'project', select: 'title description' }
    ]);

    res.status(201).json({
      success: true,
      data: meeting
    });
  } catch (error) {
    console.error('Error creating meeting:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Get all meetings for a user
// @route   GET /api/v1/meetings
// @access  Private
exports.getMeetings = async (req, res) => {
  try {
    const { status, upcoming, past } = req.query;
    let query = {
      $or: [
        { organizer: req.user.id },
        { 'participants.user': req.user.id }
      ]
    };

    // Filter by status
    if (status) {
      query.status = status;
    }

    // Filter by time
    if (upcoming === 'true') {
      query.scheduledTime = { $gte: new Date() };
    } else if (past === 'true') {
      query.scheduledTime = { $lt: new Date() };
    }

    const meetings = await Meeting.find(query)
      .populate('organizer', 'name email')
      .populate('participants.user', 'name email')
      .populate('project', 'title description')
      .sort({ scheduledTime: 1 });

    res.status(200).json({
      success: true,
      count: meetings.length,
      data: meetings
    });
  } catch (error) {
    console.error('Error fetching meetings:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Get single meeting
// @route   GET /api/v1/meetings/:id
// @access  Private
exports.getMeeting = async (req, res) => {
  try {
    const meeting = await Meeting.findById(req.params.id)
      .populate('organizer', 'name email')
      .populate('participants.user', 'name email')
      .populate('project', 'title description');

    if (!meeting) {
      return res.status(404).json({
        success: false,
        message: 'Meeting not found'
      });
    }

    // Check if user has access to this meeting
    const hasAccess = meeting.organizer._id.toString() === req.user.id ||
                     meeting.participants.some(p => p.user._id.toString() === req.user.id);

    if (!hasAccess) {
      return res.status(403).json({
        success: false,
        message: 'Not authorized to access this meeting'
      });
    }

    res.status(200).json({
      success: true,
      data: meeting
    });
  } catch (error) {
    console.error('Error fetching meeting:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Update meeting
// @route   PUT /api/v1/meetings/:id
// @access  Private
exports.updateMeeting = async (req, res) => {
  try {
    let meeting = await Meeting.findById(req.params.id);

    if (!meeting) {
      return res.status(404).json({
        success: false,
        message: 'Meeting not found'
      });
    }

    // Check if user is the organizer
    if (meeting.organizer.toString() !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: 'Not authorized to update this meeting'
      });
    }

    // Validate scheduled time if being updated
    if (req.body.scheduledTime && new Date(req.body.scheduledTime) <= new Date()) {
      return res.status(400).json({
        success: false,
        message: 'Meeting must be scheduled for a future time'
      });
    }

    meeting = await Meeting.findByIdAndUpdate(
      req.params.id,
      req.body,
      {
        new: true,
        runValidators: true
      }
    ).populate([
      { path: 'organizer', select: 'name email' },
      { path: 'participants.user', select: 'name email' },
      { path: 'project', select: 'title description' }
    ]);

    res.status(200).json({
      success: true,
      data: meeting
    });
  } catch (error) {
    console.error('Error updating meeting:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Delete meeting
// @route   DELETE /api/v1/meetings/:id
// @access  Private
exports.deleteMeeting = async (req, res) => {
  try {
    const meeting = await Meeting.findById(req.params.id);

    if (!meeting) {
      return res.status(404).json({
        success: false,
        message: 'Meeting not found'
      });
    }

    // Check if user is the organizer
    if (meeting.organizer.toString() !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: 'Not authorized to delete this meeting'
      });
    }

    await meeting.deleteOne();

    res.status(200).json({
      success: true,
      data: {}
    });
  } catch (error) {
    console.error('Error deleting meeting:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Respond to meeting invitation
// @route   PUT /api/v1/meetings/:id/respond
// @access  Private
exports.respondToMeeting = async (req, res) => {
  try {
    const { status } = req.body; // 'accepted' or 'declined'

    if (!['accepted', 'declined'].includes(status)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid response status'
      });
    }

    const meeting = await Meeting.findById(req.params.id);

    if (!meeting) {
      return res.status(404).json({
        success: false,
        message: 'Meeting not found'
      });
    }

    // Find the participant
    const participantIndex = meeting.participants.findIndex(
      p => p.user.toString() === req.user.id
    );

    if (participantIndex === -1) {
      return res.status(403).json({
        success: false,
        message: 'You are not invited to this meeting'
      });
    }

    // Update participant status
    meeting.participants[participantIndex].status = status;
    if (status === 'accepted') {
      meeting.participants[participantIndex].joinedAt = new Date();
    }

    await meeting.save();

    await meeting.populate([
      { path: 'organizer', select: 'name email' },
      { path: 'participants.user', select: 'name email' },
      { path: 'project', select: 'title description' }
    ]);

    res.status(200).json({
      success: true,
      data: meeting
    });
  } catch (error) {
    console.error('Error responding to meeting:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Start meeting
// @route   PUT /api/v1/meetings/:id/start
// @access  Private
exports.startMeeting = async (req, res) => {
  try {
    const meeting = await Meeting.findById(req.params.id);

    if (!meeting) {
      return res.status(404).json({
        success: false,
        message: 'Meeting not found'
      });
    }

    // Check if user is the organizer
    if (meeting.organizer.toString() !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: 'Only the organizer can start the meeting'
      });
    }

    meeting.status = 'ongoing';
    await meeting.save();

    res.status(200).json({
      success: true,
      data: meeting
    });
  } catch (error) {
    console.error('Error starting meeting:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    End meeting
// @route   PUT /api/v1/meetings/:id/end
// @access  Private
exports.endMeeting = async (req, res) => {
  try {
    const { notes, recordingUrl } = req.body;

    const meeting = await Meeting.findById(req.params.id);

    if (!meeting) {
      return res.status(404).json({
        success: false,
        message: 'Meeting not found'
      });
    }

    // Check if user is the organizer
    if (meeting.organizer.toString() !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: 'Only the organizer can end the meeting'
      });
    }

    meeting.status = 'completed';
    if (notes) meeting.notes = notes;
    if (recordingUrl) {
      meeting.recording = {
        url: recordingUrl,
        duration: req.body.recordingDuration || 0
      };
    }

    await meeting.save();

    res.status(200).json({
      success: true,
      data: meeting
    });
  } catch (error) {
    console.error('Error ending meeting:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};
