// controllers/profileController.js
const User = require('../models/User');
const EmployerProfile = require('../models/Employer');
const JobSeekerProfile = require('../models/JobSeeker');

// @desc    Get user profile with complete details
// @route   GET /api/v1/profile
// @access  Private
exports.getProfile = async (req, res) => {
  try {
    const user = req.user;
    
    // Get profile based on user role
    let profile = null;
    if (user.role === 'employer') {
      profile = await EmployerProfile.findOne({ user: user._id });
    } else if (user.role === 'jobseeker') {
      profile = await JobSeekerProfile.findOne({ user: user._id });
    }

    res.status(200).json({
      success: true,
      data: {
        user: {
          id: user._id,
          name: user.name,
          email: user.email,
          role: user.role,
          location: user.location,
          isVerified: user.isVerified,
          createdAt: user.createdAt
        },
        profile
      }
    });
  } catch (error) {
    console.error('Get Profile Error:', error);
    res.status(500).json({
      success: false,
      message: 'Error fetching user profile',
      error: error.message
    });
  }
};

// @desc    Update user profile
// @route   PUT /api/v1/profile
// @access  Private
exports.updateProfile = async (req, res) => {
  try {
    const user = req.user;
    const updateData = req.body;

    // Update basic user fields if provided
    const userUpdateFields = {};
    if (updateData.name) userUpdateFields.name = updateData.name;
    if (updateData.location) userUpdateFields.location = updateData.location;

    // Update user if there are user fields to update
    if (Object.keys(userUpdateFields).length > 0) {
      await User.findByIdAndUpdate(user._id, userUpdateFields, { new: true });
    }

    // Update profile based on role
    let updatedProfile = null;
    
    if (user.role === 'employer') {
      const profileUpdateFields = {};
      if (updateData.companyName !== undefined) profileUpdateFields.companyName = updateData.companyName;
      if (updateData.linkedinUrl !== undefined) profileUpdateFields.linkedinUrl = updateData.linkedinUrl;
      if (updateData.phoneNumber !== undefined) profileUpdateFields.phoneNumber = updateData.phoneNumber;
      if (updateData.bio !== undefined) profileUpdateFields.bio = updateData.bio;

      updatedProfile = await EmployerProfile.findOneAndUpdate(
        { user: user._id },
        profileUpdateFields,
        { new: true, upsert: true }
      );
    } else if (user.role === 'jobseeker') {
      const profileUpdateFields = {};
      if (updateData.bio !== undefined) profileUpdateFields.bio = updateData.bio;
      if (updateData.phoneNumber !== undefined) profileUpdateFields.phoneNumber = updateData.phoneNumber;
      if (updateData.skills !== undefined) {
        // Handle skills as array or comma-separated string
        profileUpdateFields.skills = Array.isArray(updateData.skills) 
          ? updateData.skills 
          : updateData.skills.split(',').map(skill => skill.trim());
      }
      if (updateData.experience !== undefined) profileUpdateFields.experience = updateData.experience;
      if (updateData.education !== undefined) profileUpdateFields.education = updateData.education;
      if (updateData.portfolio !== undefined) profileUpdateFields.portfolio = updateData.portfolio;

      updatedProfile = await JobSeekerProfile.findOneAndUpdate(
        { user: user._id },
        profileUpdateFields,
        { new: true, upsert: true }
      );
    }

    // Get updated user data
    const updatedUser = await User.findById(user._id);

    res.status(200).json({
      success: true,
      message: 'Profile updated successfully',
      data: {
        user: {
          id: updatedUser._id,
          name: updatedUser.name,
          email: updatedUser.email,
          role: updatedUser.role,
          location: updatedUser.location,
          isVerified: updatedUser.isVerified,
          createdAt: updatedUser.createdAt
        },
        profile: updatedProfile
      }
    });
  } catch (error) {
    console.error('Update Profile Error:', error);
    res.status(500).json({
      success: false,
      message: 'Error updating profile',
      error: error.message
    });
  }
};

// @desc    Upload profile picture
// @route   POST /api/v1/profile/picture
// @access  Private
exports.uploadProfilePicture = async (req, res) => {
  try {
    const user = req.user;
    const { profilePicture } = req.body; // Base64 string or URL

    if (!profilePicture) {
      return res.status(400).json({
        success: false,
        message: 'Please provide a profile picture'
      });
    }

    // Update profile based on role
    let updatedProfile = null;
    
    if (user.role === 'employer') {
      updatedProfile = await EmployerProfile.findOneAndUpdate(
        { user: user._id },
        { profilePicture },
        { new: true, upsert: true }
      );
    } else if (user.role === 'jobseeker') {
      updatedProfile = await JobSeekerProfile.findOneAndUpdate(
        { user: user._id },
        { profilePicture },
        { new: true, upsert: true }
      );
    }

    res.status(200).json({
      success: true,
      message: 'Profile picture updated successfully',
      data: {
        profilePicture: updatedProfile.profilePicture
      }
    });
  } catch (error) {
    console.error('Upload Profile Picture Error:', error);
    res.status(500).json({
      success: false,
      message: 'Error uploading profile picture',
      error: error.message
    });
  }
};

// @desc    Delete user account
// @route   DELETE /api/v1/profile
// @access  Private
exports.deleteAccount = async (req, res) => {
  try {
    const user = req.user;

    // Delete profile based on role
    if (user.role === 'employer') {
      await EmployerProfile.findOneAndDelete({ user: user._id });
    } else if (user.role === 'jobseeker') {
      await JobSeekerProfile.findOneAndDelete({ user: user._id });
    }

    // Delete user
    await User.findByIdAndDelete(user._id);

    res.status(200).json({
      success: true,
      message: 'Account deleted successfully'
    });
  } catch (error) {
    console.error('Delete Account Error:', error);
    res.status(500).json({
      success: false,
      message: 'Error deleting account',
      error: error.message
    });
  }
};
