// Shows List of all job listings user had created
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../services/service_joblisting_list.dart';
import '../models/job_listing.dart';
import '../utils/job_listing_adapter.dart';
import 'create_jobListing_screen.dart';

class JobListingScreen extends StatefulWidget {
  static const String id = 'job_listing';
  const JobListingScreen({super.key});

  @override
  _JobListingScreenState createState() => _JobListingScreenState();
}

class _JobListingScreenState extends State<JobListingScreen> {
  final ProjectService _projectService = ProjectService();
  late Future<List<JobListing>> _projectsFuture;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadProjects();
  }

  void _loadProjects() {
    setState(() {
      _isLoading = true;
      _projectsFuture = _projectService.fetchUserProjects();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Projects'),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            Expanded(
              child: FutureBuilder<List<JobListing>>(
                future: _projectsFuture,
                builder: (context, snapshot) {
                  if (snapshot.connectionState == ConnectionState.waiting) {
                    return const Center(child: CircularProgressIndicator());
                  } else if (snapshot.hasError) {
                    return Center(
                      child: Text(
                        'Error loading projects: ${snapshot.error}',
                        style: const TextStyle(color: Colors.red),
                      ),
                    );
                  } else if (!snapshot.hasData || snapshot.data!.isEmpty) {
                    return const Center(
                      child: Text('No projects found. Create your first project!'),
                    );
                  } else {
                    return ListView.builder(
                      itemCount: snapshot.data!.length,
                      itemBuilder: (context, index) {
                        final project = snapshot.data![index];
                        return ProjectCard(
                          project: project,
                          onEdit: () => _editProject(project),
                          onShortlist: () => _shortlistProject(project.id, !project.isShortlisted),
                          onCancel: () => _cancelProject(project.id),
                        );
                      },
                    );
                  }
                },
              ),
            ),
            const SizedBox(height: 20),
            SizedBox(
              width: double.infinity,
              height: 50,
              child: ElevatedButton(
                onPressed: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(builder: (context) => const CreateJobListingScreen()),
                  ).then((_) => _loadProjects()); // Reload projects when returning
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.amber,
                  foregroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(25),
                  ),
                ),
                child: const Text('Create New Project', style: TextStyle(fontSize: 16)),
              ),
            ),
            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  void _editProject(JobListing project) {
    // Navigate to edit project screen
    print('Editing project: ${JobListingAdapter.getProjectName(project)}');
    // Implement navigation to edit screen
  }

  Future<void> _shortlistProject(String projectId, bool shortlist) async {
    try {
      final success = await _projectService.shortlistProject(projectId, shortlist);
      if (success) {
        _loadProjects(); // Reload the projects list
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(shortlist ? 'Project shortlisted' : 'Project removed from shortlist')),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error: $e')),
      );
    }
  }

  Future<void> _cancelProject(String projectId) async {
    try {
      final success = await _projectService.deleteProject(projectId);
      if (success) {
        _loadProjects(); // Reload the projects list
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Project cancelled')),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error: $e')),
      );
    }
  }
}

class ProjectCard extends StatelessWidget {
  final JobListing project;
  final VoidCallback onEdit;
  final VoidCallback onShortlist;
  final VoidCallback onCancel;

  const ProjectCard({
    required this.project,
    required this.onEdit,
    required this.onShortlist,
    required this.onCancel,
    Key? key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              JobListingAdapter.getProjectName(project),
              style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            Text(project.title),
            const SizedBox(height: 4),
            Text(JobListingAdapter.getJobDescription(project), maxLines: 2, overflow: TextOverflow.ellipsis),
            const SizedBox(height: 8),
            Row(
              children: [
                Icon(Icons.location_on, size: 16, color: Colors.grey[600]),
                const SizedBox(width: 4),
                Text(project.location, style: TextStyle(color: Colors.grey[600])),
                const SizedBox(width: 16),
                Icon(Icons.calendar_today, size: 16, color: Colors.grey[600]),
                const SizedBox(width: 4),
                Text(
                  DateFormat('MMM d, yyyy').format(JobListingAdapter.getStartDate(project)),
                  style: TextStyle(color: Colors.grey[600]),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                IconButton(
                  icon: Icon(
                    project.isShortlisted ? Icons.star : Icons.star_border,
                    color: project.isShortlisted ? Colors.amber : null,
                  ),
                  onPressed: onShortlist,
                  tooltip: project.isShortlisted ? 'Remove from shortlist' : 'Add to shortlist',
                ),
                IconButton(
                  icon: const Icon(Icons.edit),
                  onPressed: onEdit,
                  tooltip: 'Edit project',
                ),
                IconButton(
                  icon: const Icon(Icons.delete_outline, color: Colors.red),
                  onPressed: onCancel,
                  tooltip: 'Cancel project',
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
