const express = require('express');
const { body } = require('express-validator');
const NotificationService = require('../services/notificationService');
const { protect } = require('../middlewares/auth');

const router = express.Router();

// @desc    Update user's FCM token
// @route   PUT /api/v1/notifications/token
// @access  Private
router.put('/token', 
  protect,
  [
    body('fcmToken')
      .notEmpty()
      .withMessage('FCM token is required')
  ],
  async (req, res) => {
    try {
      const { fcmToken } = req.body;
      
      const result = await NotificationService.updateUserToken(req.user.id, fcmToken);
      
      if (result.success) {
        res.status(200).json({
          success: true,
          message: 'FCM token updated successfully'
        });
      } else {
        res.status(500).json({
          success: false,
          message: result.error || 'Failed to update FCM token'
        });
      }
    } catch (error) {
      console.error('Error updating FCM token:', error);
      res.status(500).json({
        success: false,
        message: 'Server error'
      });
    }
  }
);

// @desc    Send test notification
// @route   POST /api/v1/notifications/test
// @access  Private
router.post('/test',
  protect,
  [
    body('title')
      .notEmpty()
      .withMessage('Notification title is required'),
    body('body')
      .notEmpty()
      .withMessage('Notification body is required')
  ],
  async (req, res) => {
    try {
      const { title, body, data } = req.body;
      
      const notification = {
        title,
        body,
        data: data || {}
      };
      
      const result = await NotificationService.sendToUser(req.user.id, notification);
      
      if (result.success) {
        res.status(200).json({
          success: true,
          message: 'Test notification sent successfully',
          data: result
        });
      } else {
        res.status(500).json({
          success: false,
          message: result.error || 'Failed to send notification'
        });
      }
    } catch (error) {
      console.error('Error sending test notification:', error);
      res.status(500).json({
        success: false,
        message: 'Server error'
      });
    }
  }
);

module.exports = router;
