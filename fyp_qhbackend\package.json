{"name": "quickhire-backend", "version": "1.0.0", "description": "Backend for QuickHire - Project-based hiring platform", "main": "server.js", "scripts": {"start": "node start-server.js", "dev": "nodemon start-server.js", "server": "node server.js", "get-ip": "node get-ip.js"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"bcrypt": "^5.1.0", "cors": "^2.8.5", "dotenv": "^16.0.3", "express": "^4.18.2", "jsonwebtoken": "^9.0.0", "mongoose": "^7.0.3", "nodemailer": "^6.9.1", "nodemon": "^2.0.22", "socket.io": "^4.8.1"}, "devDependencies": {"nodemon": "^3.1.9"}}