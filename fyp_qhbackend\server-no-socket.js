// server-no-socket.js - Server without socket.io for testing
const express = require('express');
const dotenv = require('dotenv');
const mongoose = require('mongoose');
const cors = require('cors');

// Load env vars
dotenv.config();

// Initialize app
const app = express();

// Route files
const authRoutes = require('./routes/authRoutes');
const profileRoutes = require('./routes/profileRoutes');
const projectRoutes = require('./routes/projectRoutes');
const messageRoutes = require('./routes/messageRoutes');
const meetingRoutes = require('./routes/meetingRoutes');
const notificationRoutes = require('./routes/notificationRoutes');
const videoCallRoutes = require('./routes/videoCallRoutes');

// Body parser
app.use(express.json());

// Enable CORS for web testing
app.use(cors({
  origin: function (origin, callback) {
    // Allow requests with no origin (like mobile apps or curl requests)
    if (!origin) return callback(null, true);

    // Allow all localhost and 127.0.0.1 origins
    if (origin.includes('localhost') || origin.includes('127.0.0.1')) {
      return callback(null, true);
    }

    // Allow other origins for development
    return callback(null, true);
  },
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization']
}));

// Connect to MongoDB
mongoose.connect(process.env.MONGO_URI)
  .then(() => console.log('MongoDB Connected'))
  .catch(err => {
    console.error('MongoDB Connection Error:', err);
    process.exit(1);
  });

// Mount routes
app.use('/api/v1/auth', authRoutes);
app.use('/api/v1/profile', profileRoutes);
app.use('/api/v1/projects', projectRoutes);
app.use('/api/v1/messages', messageRoutes);
app.use('/api/v1/meetings', meetingRoutes);
app.use('/api/v1/notifications', notificationRoutes);
app.use('/api/v1/calls', videoCallRoutes);

// Simple route for testing
app.get('/', (req, res) => {
  res.json({ 
    message: 'QuickHire API is running (no socket)',
    timestamp: new Date().toISOString(),
    endpoints: {
      auth: '/api/v1/auth',
      profile: '/api/v1/profile',
      projects: '/api/v1/projects',
      messages: '/api/v1/messages',
      meetings: '/api/v1/meetings',
      notifications: '/api/v1/notifications',
      calls: '/api/v1/calls'
    }
  });
});

// Error handling for undefined routes
app.use((req, res) => {
  console.log(`404 - Route not found: ${req.method} ${req.path}`);
  res.status(404).json({
    success: false,
    message: 'Route not found',
    path: req.path,
    method: req.method,
    timestamp: new Date().toISOString()
  });
});

// Global error handler
app.use((err, req, res, next) => {
  console.error('Error:', err);
  
  res.status(err.statusCode || 500).json({
    success: false,
    message: err.message || 'Server Error',
    timestamp: new Date().toISOString(),
    stack: process.env.NODE_ENV === 'development' ? err.stack : undefined
  });
});

// Start server
const PORT = process.env.PORT || 5001;
app.listen(PORT, () => {
  console.log(`🚀 QuickHire API Server running on port ${PORT}`);
  console.log(`📍 Base URL: http://localhost:${PORT}`);
  console.log(`🔗 API Base: http://localhost:${PORT}/api/v1`);
  console.log(`📋 Available endpoints:`);
  console.log(`   • Auth: http://localhost:${PORT}/api/v1/auth`);
  console.log(`   • Profile: http://localhost:${PORT}/api/v1/profile`);
  console.log(`   • Projects: http://localhost:${PORT}/api/v1/projects`);
  console.log(`   • Messages: http://localhost:${PORT}/api/v1/messages`);
  console.log(`   • Meetings: http://localhost:${PORT}/api/v1/meetings`);
  console.log(`   • Notifications: http://localhost:${PORT}/api/v1/notifications`);
  console.log(`   • Video Calls: http://localhost:${PORT}/api/v1/calls`);
});
