import 'package:flutter/material.dart';
import '../services/meeting_service.dart';
import '../services/video_call_service.dart';
import '../services/notification_service.dart';
import '../services/job_service.dart';

class FeatureShowcaseScreen extends StatefulWidget {
  static const String id = 'feature_showcase_screen';
  
  const FeatureShowcaseScreen({Key? key}) : super(key: key);

  @override
  State<FeatureShowcaseScreen> createState() => _FeatureShowcaseScreenState();
}

class _FeatureShowcaseScreenState extends State<FeatureShowcaseScreen> {
  final MeetingService _meetingService = MeetingService();
  final VideoCallService _videoCallService = VideoCallService();
  final NotificationService _notificationService = NotificationService();
  final JobService _jobService = JobService();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('QuickHire Features'),
        backgroundColor: Colors.blue[600],
        foregroundColor: Colors.white,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeader(),
            const SizedBox(height: 20),
            _buildFeatureSection(
              'Authentication Features',
              [
                _buildFeatureCard('Login', 'JWT-based authentication', Icons.login, Colors.green),
                _buildFeatureCard('Registration', 'Role-based signup (Employer/JobSeeker)', Icons.person_add, Colors.green),
                _buildFeatureCard('Account Verification', 'OTP-based email verification', Icons.verified, Colors.green),
                _buildFeatureCard('Profile Update', 'Update user details with backend sync', Icons.edit, Colors.green),
              ],
            ),
            const SizedBox(height: 20),
            _buildFeatureSection(
              'Job Management Features',
              [
                _buildFeatureCard('Job Listing Management', 'CRUD operations for job postings', Icons.work, Colors.blue),
                _buildFeatureCard('Matching Algorithm', 'Skill-based job matching', Icons.psychology, Colors.blue),
              ],
            ),
            const SizedBox(height: 20),
            _buildFeatureSection(
              'Communication Features',
              [
                _buildFeatureCard('In-app Messaging', 'Real-time chat with Socket.io', Icons.message, Colors.orange),
                _buildFeatureCard('Schedule Meetings', 'Meeting management system', Icons.calendar_today, Colors.orange),
                _buildFeatureCard('In-app Calling', 'WebRTC video/audio calls', Icons.video_call, Colors.orange),
                _buildFeatureCard('Push Notifications', 'FCM-based notifications', Icons.notifications, Colors.orange),
              ],
            ),
            const SizedBox(height: 20),
            _buildTestSection(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.blue[600]!, Colors.blue[800]!],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'QuickHire App',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            'All Features Completed ✅',
            style: TextStyle(
              fontSize: 18,
              color: Colors.white,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            'Frontend + Backend + Integration',
            style: TextStyle(
              fontSize: 14,
              color: Colors.white70,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFeatureSection(String title, List<Widget> features) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: const TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 12),
        ...features,
      ],
    );
  }

  Widget _buildFeatureCard(String title, String description, IconData icon, Color color) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: color.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(icon, color: color, size: 24),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.black87,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  description,
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
          Icon(
            Icons.check_circle,
            color: Colors.green[600],
            size: 20,
          ),
        ],
      ),
    );
  }

  Widget _buildTestSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Test Features',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: ElevatedButton.icon(
                onPressed: _testNotification,
                icon: const Icon(Icons.notifications),
                label: const Text('Test Notification'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.orange[600],
                  foregroundColor: Colors.white,
                ),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: ElevatedButton.icon(
                onPressed: _testJobMatching,
                icon: const Icon(Icons.work),
                label: const Text('Test Job Match'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue[600],
                  foregroundColor: Colors.white,
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        SizedBox(
          width: double.infinity,
          child: ElevatedButton.icon(
            onPressed: _showFeatureDetails,
            icon: const Icon(Icons.info),
            label: const Text('View Implementation Details'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green[600],
              foregroundColor: Colors.white,
            ),
          ),
        ),
      ],
    );
  }

  void _testNotification() async {
    try {
      final result = await _notificationService.sendTestNotification(
        title: 'QuickHire Test',
        body: 'All features are working perfectly!',
        data: {'type': 'test', 'feature': 'notification'},
      );
      
      _showSnackBar(
        result['success'] ? 'Test notification sent!' : 'Notification test failed',
        result['success'] ? Colors.green : Colors.red,
      );
    } catch (e) {
      _showSnackBar('Error testing notification: $e', Colors.red);
    }
  }

  void _testJobMatching() async {
    try {
      final result = await _jobService.getMatchedJobListings();
      _showSnackBar('Found ${result.length} matched jobs!', Colors.blue);
    } catch (e) {
      _showSnackBar('Error testing job matching: $e', Colors.red);
    }
  }

  void _showFeatureDetails() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Implementation Status'),
        content: const SingleChildScrollView(
          child: Text(
            '✅ Authentication: Complete\n'
            '✅ Job Management: Complete\n'
            '✅ Messaging: Complete\n'
            '✅ Meetings: Complete\n'
            '✅ Video Calling: Complete\n'
            '✅ Notifications: Complete\n'
            '✅ Profile Update: Complete\n\n'
            'All features have:\n'
            '• Backend API endpoints\n'
            '• Frontend services\n'
            '• Database models\n'
            '• Real-time integration\n'
            '• Error handling\n'
            '• Authentication\n'
            '• Validation',
            style: TextStyle(fontSize: 14),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _showSnackBar(String message, Color color) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: color,
        duration: const Duration(seconds: 2),
      ),
    );
  }
}
