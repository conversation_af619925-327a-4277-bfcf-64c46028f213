// routes/profileRoutes.js
const express = require('express');
const {
  getProfile,
  updateProfile,
  uploadProfilePicture,
  deleteAccount
} = require('../controllers/profileController');
const { protect } = require('../middlewares/auth');

const router = express.Router();

// All profile routes require authentication
router.use(protect);

// Profile routes
router.route('/')
  .get(getProfile)
  .put(updateProfile)
  .delete(deleteAccount);

router.post('/picture', uploadProfilePicture);

module.exports = router;
