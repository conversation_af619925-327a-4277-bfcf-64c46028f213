// Simple API test script
const http = require('http');

function testEndpoint(path, method = 'GET', data = null) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: 5000,
      path: path,
      method: method,
      headers: {
        'Content-Type': 'application/json',
      }
    };

    const req = http.request(options, (res) => {
      let body = '';
      res.on('data', (chunk) => {
        body += chunk;
      });
      res.on('end', () => {
        resolve({
          status: res.statusCode,
          body: body,
          path: path
        });
      });
    });

    req.on('error', (err) => {
      reject(err);
    });

    if (data) {
      req.write(JSON.stringify(data));
    }
    req.end();
  });
}

async function runTests() {
  console.log('🧪 Testing QuickHire API endpoints...\n');

  const tests = [
    { path: '/', name: 'Root endpoint' },
    { path: '/api/v1/auth', name: 'Auth endpoint' },
    { path: '/api/v1/projects', name: 'Projects endpoint' },
    { path: '/api/v1/messages', name: 'Messages endpoint' },
    { path: '/api/v1/meetings', name: 'Meetings endpoint' },
    { path: '/api/v1/calls', name: 'Video calls endpoint' },
    { path: '/api/v1/notifications', name: 'Notifications endpoint' },
  ];

  for (const test of tests) {
    try {
      const result = await testEndpoint(test.path);
      const status = result.status === 200 ? '✅' : 
                    result.status === 401 ? '🔒' : 
                    result.status === 404 ? '❌' : '⚠️';
      
      console.log(`${status} ${test.name}: ${result.status} - ${test.path}`);
      
      if (result.status === 200 && test.path === '/') {
        console.log(`   Response: ${result.body}`);
      }
    } catch (error) {
      console.log(`❌ ${test.name}: Connection failed - ${test.path}`);
      console.log(`   Error: ${error.message}`);
    }
  }

  console.log('\n📋 Status Code Legend:');
  console.log('   ✅ 200 - OK (working)');
  console.log('   🔒 401 - Unauthorized (needs auth token)');
  console.log('   ❌ 404 - Not Found (check route)');
  console.log('   ⚠️  Other - Check server logs');
  
  console.log('\n💡 If you see 🔒 401 errors, that\'s normal for protected routes!');
}

// Check if server is running first
console.log('🔍 Checking if server is running on http://localhost:5000...\n');
runTests().catch(error => {
  console.log('❌ Failed to connect to server. Make sure the backend is running!');
  console.log('   Run: npm start or npm run dev');
});
