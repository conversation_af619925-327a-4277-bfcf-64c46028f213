import 'package:socket_io_client/socket_io_client.dart' as IO;
import 'package:get/get.dart';
import '../config/app_config.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';

class ChatService extends GetxService {
  late IO.Socket socket;

  final messages = <Map<String, dynamic>>[].obs;
  final chatRooms = <Map<String, dynamic>>[].obs;
  final isConnected = false.obs;
  final connectionError = ''.obs;
  
  // Static data for demo purposes
  final Map<String, List<Map<String, dynamic>>> _staticMessages = {
    'room1': [
      {
        'senderId': 'other-user-1',
        'message': 'Hello! I saw your profile and I think you would be a great fit for our company.',
        'timestamp': DateTime.now().subtract(const Duration(days: 1, hours: 2)).toIso8601String(),
      },
      {
        'senderId': 'your-user-id',
        'message': 'Hi there! Thank you for reaching out. I would love to hear more about the opportunity.',
        'timestamp': DateTime.now().subtract(const Duration(days: 1, hours: 1, minutes: 45)).toIso8601String(),
      },
      {
        'senderId': 'other-user-1',
        'message': 'Great! We are looking for someone with your skills. Are you available for an interview next week?',
        'timestamp': DateTime.now().subtract(const Duration(days: 1, hours: 1, minutes: 30)).toIso8601String(),
      },
      {
        'senderId': 'your-user-id',
        'message': 'Yes, I am available. What day and time works best for you?',
        'timestamp': DateTime.now().subtract(const Duration(days: 1, hours: 1)).toIso8601String(),
      },
    ],
    'room2': [
      {
        'senderId': 'other-user-2',
        'message': 'Hi, I noticed you have experience with Flutter development.',
        'timestamp': DateTime.now().subtract(const Duration(hours: 5)).toIso8601String(),
      },
      {
        'senderId': 'your-user-id',
        'message': 'Yes, I have been working with Flutter for about 2 years now.',
        'timestamp': DateTime.now().subtract(const Duration(hours: 4, minutes: 50)).toIso8601String(),
      },
      {
        'senderId': 'other-user-2',
        'message': 'That\'s great! We have a project that might interest you.',
        'timestamp': DateTime.now().subtract(const Duration(hours: 4, minutes: 45)).toIso8601String(),
      },
    ],
    'room3': [
      {
        'senderId': 'other-user-3',
        'message': 'Hello, are you still looking for job opportunities?',
        'timestamp': DateTime.now().subtract(const Duration(minutes: 30)).toIso8601String(),
      },
      {
        'senderId': 'your-user-id',
        'message': 'Yes, I am actively looking for new opportunities.',
        'timestamp': DateTime.now().subtract(const Duration(minutes: 25)).toIso8601String(),
      },
      {
        'senderId': 'other-user-3',
        'message': 'Perfect! I have a position that matches your profile. Would you like to discuss it further?',
        'timestamp': DateTime.now().subtract(const Duration(minutes: 20)).toIso8601String(),
      },
      {
        'senderId': 'your-user-id',
        'message': 'Absolutely! I would love to hear more details.',
        'timestamp': DateTime.now().subtract(const Duration(minutes: 15)).toIso8601String(),
      },
    ],
  };

  // Constructor to immediately load static data
  ChatService() {
    _loadStaticData();
  }

  Future<ChatService> init() async {
    try {
      // Make sure static data is loaded
      if (chatRooms.isEmpty) {
        _loadStaticData();
      }
      
      // Print debug info
      print('ChatService initialized with ${chatRooms.length} static chat rooms');
      chatRooms.forEach((room) {
        print('Room: ${room['roomName']}, Last message: ${room['lastMessage']}');
      });
      
      // Still attempt to connect to socket for future real implementation
      _initSocket();
      
      return this;
    } catch (e) {
      print('Error initializing chat service: $e');
      connectionError.value = 'Error initializing: $e';
      
      // Ensure static data is loaded even if there's an error
      if (chatRooms.isEmpty) {
        _loadStaticData();
      }
      
      return this;
    }
  }
  
  // Public method to load static data
  void loadStaticData() {
    _loadStaticData();
  }
  
  void _loadStaticData() {
    print('Loading static chat data...');
    
    // Load static chat rooms
    chatRooms.assignAll([
      {
        'roomId': 'room1',
        'roomName': 'Tech Innovations Inc.',
        'lastMessage': 'Yes, I am available. What day and time works best for you?',
        'unreadCount': 0,
        'timestamp': DateTime.now().subtract(const Duration(days: 1, hours: 1)).toIso8601String(),
      },
      {
        'roomId': 'room2',
        'roomName': 'Flutter Projects',
        'lastMessage': 'That\'s great! We have a project that might interest you.',
        'unreadCount': 2,
        'timestamp': DateTime.now().subtract(const Duration(hours: 4, minutes: 45)).toIso8601String(),
      },
      {
        'roomId': 'room3',
        'roomName': 'Job Connect',
        'lastMessage': 'Absolutely! I would love to hear more details.',
        'unreadCount': 0,
        'timestamp': DateTime.now().subtract(const Duration(minutes: 15)).toIso8601String(),
      },
    ]);
    
    print('Loaded ${chatRooms.length} static chat rooms');
  }
  
  void _initSocket() {
    // Get user ID and token for authentication
    SharedPreferences.getInstance().then((prefs) {
      final userId = prefs.getString('user_id') ?? '';
      final token = prefs.getString('token') ?? '';
      
      print('Initializing chat service with userId: $userId');
      
      // Configure socket with auth data
      socket = IO.io(AppConfig.socketUrl, <String, dynamic>{
        'transports': ['websocket'],
        'autoConnect': false,
        'query': {
          'userId': userId,
          'token': token,
        },
      });

      // Set up socket event handlers
      _setupSocketEventHandlers();
      
      // Connect to socket
      print('Connecting to socket...');
      socket.connect();
    });
  }
  
  void _setupSocketEventHandlers() {
    socket.onConnecting((_) => print('Socket connecting...'));
    socket.onConnectError((data) {
      print('Socket connect error: $data');
      connectionError.value = 'Connection error: $data';
    });
    socket.onError((data) {
      print('Socket error: $data');
      connectionError.value = 'Socket error: $data';
    });

    socket.onConnect((_) {
      print('Connected to socket');
      isConnected.value = true;
      connectionError.value = '';
      fetchChatRooms();
    });

    socket.on('receive_message', (data) {
      print('Received message: $data');
      messages.add(data);
    });

    socket.on('chat_rooms', (data) {
      print('Received chat rooms: $data');
      try {
        // For demo, we'll keep using static data
        // chatRooms.assignAll(List<Map<String, dynamic>>.from(data));
        print('Chat rooms would be updated from server');
      } catch (e) {
        print('Error processing chat rooms: $e');
      }
    });

    socket.onDisconnect((_) {
      print('Disconnected from socket');
      isConnected.value = false;
    });
  }

  void sendMessage(String roomId, String message, String senderId) {
    // For demo, add message to static data
    final newMessage = {
      'senderId': senderId,
      'message': message,
      'timestamp': DateTime.now().toIso8601String(),
    };
    
    // Add to messages list
    messages.add(newMessage);
    
    // Update last message in chat room
    final index = chatRooms.indexWhere((room) => room['roomId'] == roomId);
    if (index != -1) {
      final updatedRoom = Map<String, dynamic>.from(chatRooms[index]);
      updatedRoom['lastMessage'] = message;
      updatedRoom['timestamp'] = newMessage['timestamp'];
      chatRooms[index] = updatedRoom;
    }
    
    print('Sent message to room $roomId: $message');
    
    // Still try to send via socket for future real implementation
    if (isConnected.value) {
      socket.emit('send_message', {
        'roomId': roomId,
        'message': message,
        'senderId': senderId,
        'timestamp': newMessage['timestamp'],
      });
    }
  }

  void fetchChatRooms() {
    // For demo, we already loaded static data
    print('Fetching chat rooms (static data already loaded)');
    
    // Still try to fetch via socket for future real implementation
    if (isConnected.value) {
      socket.emit('get_chat_rooms');
    }
  }

  void joinRoom(String roomId) {
    // For demo, load static messages for this room
    messages.clear();
    if (_staticMessages.containsKey(roomId)) {
      messages.assignAll(_staticMessages[roomId]!);
      print('Loaded ${messages.length} static messages for room $roomId');
    }
    
    // Still try to join via socket for future real implementation
    if (isConnected.value) {
      print('Joining room: $roomId');
      socket.emit('join_room', roomId);
    }
  }

  void leaveRoom(String roomId) {
    // For demo, just clear messages
    messages.clear();
    
    // Still try to leave via socket for future real implementation
    if (isConnected.value) {
      print('Leaving room: $roomId');
      socket.emit('leave_room', roomId);
    }
  }

  void reconnect() {
    print('Attempting to reconnect...');
    if (!socket.connected) {
      socket.connect();
    }
  }

  @override
  void onClose() {
    print('Closing chat service');
    socket.dispose();
    super.onClose();
  }
}
