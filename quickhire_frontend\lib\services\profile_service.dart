import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import '../config/app_config.dart';

class ProfileService {
  // Base URL for profile API calls
  final String baseUrl = '${AppConfig.baseUrl}/profile';

  // Headers for API authentication
  Future<Map<String, String>> get _headers async {
    final token = await _getAuthToken();
    return {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer $token',
    };
  }

  // Get auth token from secure storage
  Future<String> _getAuthToken() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString('token') ?? '';
  }

  // Get user profile
  Future<Map<String, dynamic>> getProfile() async {
    try {
      final headers = await _headers;
      final response = await http.get(
        Uri.parse(baseUrl),
        headers: headers,
      ).timeout(const Duration(seconds: 10));

      if (response.statusCode == 200) {
        return json.decode(response.body);
      } else if (response.statusCode == 401) {
        throw Exception('Unauthorized. Please login again.');
      } else {
        throw Exception('Failed to load profile: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error fetching profile: $e');
    }
  }

  // Update user profile
  Future<Map<String, dynamic>> updateProfile(Map<String, dynamic> profileData) async {
    try {
      final headers = await _headers;
      final response = await http.put(
        Uri.parse(baseUrl),
        headers: headers,
        body: json.encode(profileData),
      ).timeout(const Duration(seconds: 10));

      if (response.statusCode == 200) {
        return json.decode(response.body);
      } else if (response.statusCode == 401) {
        throw Exception('Unauthorized. Please login again.');
      } else {
        final errorData = json.decode(response.body);
        throw Exception(errorData['message'] ?? 'Failed to update profile');
      }
    } catch (e) {
      throw Exception('Error updating profile: $e');
    }
  }

  // Upload profile picture
  Future<Map<String, dynamic>> uploadProfilePicture(String base64Image) async {
    try {
      final headers = await _headers;
      final response = await http.post(
        Uri.parse('$baseUrl/picture'),
        headers: headers,
        body: json.encode({
          'profilePicture': base64Image,
        }),
      ).timeout(const Duration(seconds: 30));

      if (response.statusCode == 200) {
        return json.decode(response.body);
      } else if (response.statusCode == 401) {
        throw Exception('Unauthorized. Please login again.');
      } else {
        final errorData = json.decode(response.body);
        throw Exception(errorData['message'] ?? 'Failed to upload profile picture');
      }
    } catch (e) {
      throw Exception('Error uploading profile picture: $e');
    }
  }

  // Delete account
  Future<bool> deleteAccount() async {
    try {
      final headers = await _headers;
      final response = await http.delete(
        Uri.parse(baseUrl),
        headers: headers,
      ).timeout(const Duration(seconds: 10));

      return response.statusCode == 200 || response.statusCode == 204;
    } catch (e) {
      throw Exception('Error deleting account: $e');
    }
  }

  // Save job/project
  Future<bool> saveProject(String projectId) async {
    try {
      final headers = await _headers;
      final response = await http.post(
        Uri.parse('${AppConfig.baseUrl}/projects/$projectId/save'),
        headers: headers,
      ).timeout(const Duration(seconds: 10));

      return response.statusCode == 200;
    } catch (e) {
      throw Exception('Error saving project: $e');
    }
  }

  // Get saved projects
  Future<List<Map<String, dynamic>>> getSavedProjects() async {
    try {
      final headers = await _headers;
      final response = await http.get(
        Uri.parse('${AppConfig.baseUrl}/projects/saved'),
        headers: headers,
      ).timeout(const Duration(seconds: 10));

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return List<Map<String, dynamic>>.from(data['data'] ?? []);
      } else if (response.statusCode == 401) {
        throw Exception('Unauthorized. Please login again.');
      } else {
        throw Exception('Failed to load saved projects: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error fetching saved projects: $e');
    }
  }

  // Apply to project
  Future<bool> applyToProject(String projectId) async {
    try {
      final headers = await _headers;
      final response = await http.post(
        Uri.parse('${AppConfig.baseUrl}/projects/$projectId/accept'),
        headers: headers,
      ).timeout(const Duration(seconds: 10));

      return response.statusCode == 200;
    } catch (e) {
      throw Exception('Error applying to project: $e');
    }
  }

  // Get matched projects for jobseekers
  Future<List<Map<String, dynamic>>> getMatchedProjects() async {
    try {
      final headers = await _headers;
      final response = await http.get(
        Uri.parse('${AppConfig.baseUrl}/projects/matches/find'),
        headers: headers,
      ).timeout(const Duration(seconds: 10));

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return List<Map<String, dynamic>>.from(data['data'] ?? []);
      } else if (response.statusCode == 401) {
        throw Exception('Unauthorized. Please login again.');
      } else {
        throw Exception('Failed to load matched projects: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error fetching matched projects: $e');
    }
  }
}
