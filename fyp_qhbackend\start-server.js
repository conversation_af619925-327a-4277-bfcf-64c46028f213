// Startup script for QuickHire backend server
const os = require('os');

function getLocalIP() {
  const interfaces = os.networkInterfaces();
  
  for (const name of Object.keys(interfaces)) {
    for (const interface of interfaces[name]) {
      if (interface.family === 'IPv4' && !interface.internal) {
        return interface.address;
      }
    }
  }
  return 'localhost';
}

const localIP = getLocalIP();

// Read port from environment or default to 5001
const port = process.env.PORT || 5001;

// Set the PORT environment variable for the server
process.env.PORT = port;

console.log('🚀 Starting QuickHire Backend Server...\n');
console.log('📋 Server Information:');
console.log(`   • Local URL: http://localhost:${port}`);
console.log(`   • Network URL: http://${localIP}:${port}`);
console.log(`   • API Base: http://localhost:${port}/api/v1`);
console.log('\n🌐 Chrome Web Testing:');
console.log(`   • Backend: http://localhost:${port}`);
console.log(`   • Flutter Web: Run "flutter run -d web" in quickhire_frontend/`);
console.log(`   • Then open: http://localhost:3000 (or Flutter's assigned port)`);
console.log('\n📱 Other Flutter Configurations:');
console.log(`   • Android Emulator: serverIP = "********"`);
console.log(`   • Physical Device: serverIP = "${localIP}"`);
console.log(`   • Web Testing: serverIP = "localhost" ✅ (already configured)`);
console.log('\n🔗 API Endpoints:');
console.log(`   • Auth: http://localhost:${port}/api/v1/auth`);
console.log(`   • Projects: http://localhost:${port}/api/v1/projects`);
console.log(`   • Messages: http://localhost:${port}/api/v1/messages`);
console.log(`   • Meetings: http://localhost:${port}/api/v1/meetings`);
console.log(`   • Notifications: http://localhost:${port}/api/v1/notifications`);
console.log(`   • Video Calls: http://localhost:${port}/api/v1/calls`);
console.log('\n⚡ Starting server...\n');

// Start the actual server
require('./server.js');
