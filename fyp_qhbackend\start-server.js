// Startup script for QuickHire backend server
const os = require('os');

function getLocalIP() {
  const interfaces = os.networkInterfaces();
  
  for (const name of Object.keys(interfaces)) {
    for (const interface of interfaces[name]) {
      if (interface.family === 'IPv4' && !interface.internal) {
        return interface.address;
      }
    }
  }
  return 'localhost';
}

const localIP = getLocalIP();

console.log('🚀 Starting QuickHire Backend Server...\n');
console.log('📋 Server Information:');
console.log(`   • Local URL: http://localhost:5000`);
console.log(`   • Network URL: http://${localIP}:5000`);
console.log(`   • API Base: http://${localIP}:5000/api/v1`);
console.log('\n📱 Flutter Configuration:');
console.log(`   • For Android Emulator: serverIP = "********"`);
console.log(`   • For Physical Device: serverIP = "${localIP}"`);
console.log(`   • For Web Testing: serverIP = "localhost"`);
console.log('\n🔗 Available Endpoints:');
console.log(`   • Auth: http://${localIP}:5000/api/v1/auth`);
console.log(`   • Projects: http://${localIP}:5000/api/v1/projects`);
console.log(`   • Messages: http://${localIP}:5000/api/v1/messages`);
console.log(`   • Meetings: http://${localIP}:5000/api/v1/meetings`);
console.log(`   • Notifications: http://${localIP}:5000/api/v1/notifications`);
console.log(`   • Video Calls: http://${localIP}:5000/api/v1/calls`);
console.log('\n⚡ Starting server...\n');

// Start the actual server
require('./server.js');
