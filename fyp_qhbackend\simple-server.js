// simple-server.js - Minimal server for testing
const express = require('express');
const cors = require('cors');

const app = express();

// Body parser
app.use(express.json());

// Enable CORS
app.use(cors({
  origin: true,
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization']
}));

// Test route
app.get('/', (req, res) => {
  res.json({ message: 'Simple server is running!' });
});

// Test auth route
app.get('/api/v1/auth/test', (req, res) => {
  res.json({ message: 'Auth route working!' });
});

// Test profile route
app.get('/api/v1/profile/test', (req, res) => {
  res.json({ message: 'Profile route working!' });
});

// Load and mount actual routes
try {
  console.log('Loading routes...');
  
  const authRoutes = require('./routes/authRoutes');
  const profileRoutes = require('./routes/profileRoutes');
  const projectRoutes = require('./routes/projectRoutes');
  
  app.use('/api/v1/auth', authRoutes);
  app.use('/api/v1/profile', profileRoutes);
  app.use('/api/v1/projects', projectRoutes);
  
  console.log('✅ Routes loaded successfully');
} catch (error) {
  console.log('❌ Error loading routes:', error.message);
}

// 404 handler
app.use((req, res) => {
  console.log(`404 - Route not found: ${req.method} ${req.path}`);
  res.status(404).json({
    success: false,
    message: 'Route not found',
    path: req.path,
    method: req.method
  });
});

// Error handler
app.use((err, req, res, next) => {
  console.error('Error:', err);
  res.status(500).json({
    success: false,
    message: err.message
  });
});

const PORT = 5001;
app.listen(PORT, () => {
  console.log(`🚀 Simple server running on port ${PORT}`);
  console.log(`📍 Test URL: http://localhost:${PORT}`);
  console.log(`🔗 Auth test: http://localhost:${PORT}/api/v1/auth/test`);
  console.log(`👤 Profile test: http://localhost:${PORT}/api/v1/profile/test`);
});
