import 'package:flutter/material.dart';
import '../services/job_service.dart';
import '../models/job_listing.dart';
import '../utils/job_listing_adapter.dart';
import '../widgets/bottom_nav_bar.dart';
import 'jobseeker_swipe.dart';
import 'chat_list_screen.dart';
import 'package:shared_preferences/shared_preferences.dart';

class JsDashboard extends StatefulWidget {
  static const String id = 'js_dashboard';
  const JsDashboard({super.key});

  @override
  State<JsDashboard> createState() => _JsDashboardState();
}

class _JsDashboardState extends State<JsDashboard> {
  final JobService _jobService = JobService();
  List<JobListing> _recentMatches = [];
  bool _isLoading = true;
  
  // Add user data variables
  String _firstName = '';
  String _lastName = '';
  String _email = '';
  String _location = '';
  String _profileImageUrl = '';

  @override
  void initState() {
    super.initState();
    _loadUserData();
    _loadRecentMatches();
  }

  // Add method to load user data
  Future<void> _loadUserData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      setState(() {
        _firstName = prefs.getString('first_name') ?? '';
        _lastName = prefs.getString('last_name') ?? '';
        _email = prefs.getString('email') ?? '';
        _location = prefs.getString('location') ?? '';
        _profileImageUrl = prefs.getString('profile_image_url') ?? '';
      });
    } catch (e) {
      print('Error loading user data: $e');
    }
  }

  Future<void> _loadRecentMatches() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Get matched projects from the consolidated service
      final jobListings = await _jobService.getMatchedJobListings();
      setState(() {
        // Take only the first 3 for the dashboard preview
        _recentMatches = jobListings.take(3).toList();
        _isLoading = false;
      });
    } catch (e) {
      print('Error loading matched job listings: $e');
      // For demo purposes, load mock data if API fails
      final mockListings = await _jobService.getMockJobListings();
      setState(() {
        _recentMatches = mockListings.take(3).toList();
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      // Remove the AppBar completely
      body: _isLoading
          ? const Center(child: CircularProgressIndicator(color: Colors.amber))
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Add some padding at the top for status bar
                  const SizedBox(height: 40),
                  
                  // Welcome section moved to the top
                  Text(
                    'Welcome back${_firstName.isNotEmpty ? ", $_firstName" : ""}!',
                    style: const TextStyle(
                      fontSize: 28,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  const Text(
                    'Here are some job opportunities that match your profile',
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.grey,
                    ),
                  ),
                  const SizedBox(height: 24),
                  
                  // User profile section
                  Center(
                    child: Column(
                      children: [
                        // Profile photo with edit option
                        Stack(
                          children: [
                            CircleAvatar(
                              radius: 50,
                              backgroundColor: Colors.grey.shade200,
                              backgroundImage: _profileImageUrl.isNotEmpty
                                  ? NetworkImage(_profileImageUrl) as ImageProvider
                                  : const AssetImage('assets/default_profile.png'),
                            ),
                            Positioned(
                              bottom: 0,
                              right: 0,
                              child: Container(
                                decoration: BoxDecoration(
                                  color: Colors.amber,
                                  shape: BoxShape.circle,
                                  border: Border.all(color: Colors.white, width: 2),
                                ),
                                child: IconButton(
                                  icon: const Icon(Icons.camera_alt, color: Colors.white, size: 20),
                                  onPressed: () {
                                    // Show photo upload options
                                    _showPhotoUploadOptions(context);
                                  },
                                ),
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 12),
                        
                        // User name
                        Text(
                          _firstName.isNotEmpty || _lastName.isNotEmpty
                              ? '$_firstName $_lastName'
                              : 'Update your profile',
                          style: const TextStyle(
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        
                        if (_location.isNotEmpty)
                          Padding(
                            padding: const EdgeInsets.only(top: 4.0),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                const Icon(Icons.location_on, size: 16, color: Colors.grey),
                                const SizedBox(width: 4),
                                Text(
                                  _location,
                                  style: const TextStyle(color: Colors.grey),
                                ),
                              ],
                            ),
                          ),
                        
                        const SizedBox(height: 8),
                        
                        // Edit profile button
                        OutlinedButton.icon(
                          icon: const Icon(Icons.edit, size: 16),
                          label: const Text('Edit Profile'),
                          onPressed: () {
                            // Navigate to edit profile screen
                            _navigateToEditProfile(context);
                          },
                          style: OutlinedButton.styleFrom(
                            foregroundColor: Colors.amber,
                            side: const BorderSide(color: Colors.amber),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(20),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  
                  const SizedBox(height: 24),
                  const Divider(),
                  const SizedBox(height: 16),

                  // Quick action buttons
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      _buildActionButton(
                        icon: Icons.work,
                        label: 'Find Jobs',
                        onTap: () {
                          Navigator.pushNamed(context, JobSeekerSwipeScreen.id);
                        },
                      ),
                      _buildActionButton(
                        icon: Icons.favorite,
                        label: 'Saved Jobs',
                        onTap: () {
                          // Navigate to saved jobs
                        },
                      ),
                      _buildActionButton(
                        icon: Icons.person,
                        label: 'Profile',
                        onTap: () {
                          // Navigate to profile
                        },
                      ),
                    ],
                  ),
                  const SizedBox(height: 24),

                  // Recent matches section
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text(
                        'Recent Matches',
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      TextButton(
                        onPressed: () {
                          Navigator.pushNamed(context, JobSeekerSwipeScreen.id);
                        },
                        child: const Text(
                          'See All',
                          style: TextStyle(color: Colors.amber),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),

                  // Job listings
                  _recentMatches.isEmpty
                      ? const Center(
                          child: Padding(
                            padding: EdgeInsets.all(16.0),
                            child: Text(
                              'No job matches found. Start swiping to find jobs!',
                              textAlign: TextAlign.center,
                              style: TextStyle(fontSize: 16),
                            ),
                          ),
                        )
                      : Column(
                          children: _recentMatches
                              .map((job) => _buildJobCard(job))
                              .toList(),
                        ),
                ],
              ),
            ),
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.amber.withOpacity(0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(icon, color: Colors.amber, size: 28),
          ),
          const SizedBox(height: 8),
          Text(
            label,
            style: const TextStyle(fontSize: 14),
          ),
        ],
      ),
    );
  }

  Widget _buildJobCard(JobListing job) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Text(
                    job.title,
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.amber.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    JobListingAdapter.getEmploymentType(job),
                    style: const TextStyle(
                      fontSize: 12,
                      color: Colors.amber,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              job.employer,
              style: const TextStyle(
                fontSize: 16,
                color: Colors.grey,
              ),
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                const Icon(Icons.location_on, size: 16, color: Colors.grey),
                const SizedBox(width: 4),
                Text(
                  job.location,
                  style: const TextStyle(color: Colors.grey),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                const Icon(Icons.work, size: 16, color: Colors.grey),
                const SizedBox(width: 4),
                Text(
                  JobListingAdapter.getExperienceLevel(job),
                  style: const TextStyle(color: Colors.grey),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                OutlinedButton(
                  onPressed: () {
                    // Show job details
                  },
                  style: OutlinedButton.styleFrom(
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(20),
                    ),
                  ),
                  child: const Text('Details'),
                ),
                ElevatedButton(
                  onPressed: () async {
                    try {
                      final success = await _jobService.acceptJobListing(job.id);
                      if (success) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(content: Text('Applied to ${job.title}')),
                        );
                      }
                    } catch (e) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(content: Text('Error applying to job: $e')),
                      );
                    }
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.amber,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(20),
                    ),
                  ),
                  child: const Text('Apply'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  // Method to show photo upload options
  void _showPhotoUploadOptions(BuildContext context) {
    showModalBottomSheet(
      context: context,
      builder: (context) {
        return SafeArea(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ListTile(
                leading: const Icon(Icons.photo_camera),
                title: const Text('Take a photo'),
                onTap: () {
                  Navigator.pop(context);
                  // Implement camera functionality
                  _pickImageFromCamera();
                },
              ),
              ListTile(
                leading: const Icon(Icons.photo_library),
                title: const Text('Choose from gallery'),
                onTap: () {
                  Navigator.pop(context);
                  // Implement gallery functionality
                  _pickImageFromGallery();
                },
              ),
              if (_hasExistingPhoto()) // Only show if user already has a photo
                ListTile(
                  leading: const Icon(Icons.delete, color: Colors.red),
                  title: const Text('Remove photo', style: TextStyle(color: Colors.red)),
                  onTap: () {
                    Navigator.pop(context);
                    // Implement remove photo functionality
                    _removePhoto();
                  },
                ),
            ],
          ),
        );
      },
    );
  }

  // Placeholder methods for image picking functionality
  Future<void> _pickImageFromCamera() async {
    // Implement camera image picking
    // You'll need to add image_picker package to your pubspec.yaml
    // Example:
    // final ImagePicker _picker = ImagePicker();
    // final XFile? image = await _picker.pickImage(source: ImageSource.camera);
    // if (image != null) {
    //   // Upload image to server or save locally
    // }
  }

  Future<void> _pickImageFromGallery() async {
    // Implement gallery image picking
    // Example:
    // final ImagePicker _picker = ImagePicker();
    // final XFile? image = await _picker.pickImage(source: ImageSource.gallery);
    // if (image != null) {
    //   // Upload image to server or save locally
    // }
  }

  void _removePhoto() {
    // Implement photo removal
    setState(() {
      // Reset photo URL or path
    });
  }

  bool _hasExistingPhoto() {
    // Check if user has an existing photo
    return false; // Replace with actual check
  }

  void _navigateToEditProfile(BuildContext context) async {
    // Navigate to edit profile screen
    // Example:
    // await Navigator.pushNamed(context, EditProfileScreen.id);
    
    // Refresh user data when returning from edit profile screen
    _loadUserData();
  }
}
