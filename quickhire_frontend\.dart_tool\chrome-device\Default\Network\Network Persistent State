{"net": {"http_server_properties": {"servers": [{"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["FAAAABAAAABodHRwOi8vbG9jYWxob3N0", false, 0], "server": "https://www.gstatic.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "server": "https://accounts.google.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["MAAAACsAAABodHRwczovL29wdGltaXphdGlvbmd1aWRlLXBhLmdvb2dsZWFwaXMuY29tAA==", false, 0], "network_stats": {"srtt": 53702}, "server": "https://optimizationguide-pa.googleapis.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["FAAAABAAAABodHRwOi8vbG9jYWxob3N0", false, 0], "network_stats": {"srtt": 56537}, "server": "https://content-autofill.googleapis.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["FAAAABAAAABodHRwOi8vbG9jYWxob3N0", false, 0], "network_stats": {"srtt": 60625}, "server": "https://fonts.gstatic.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13396352918322286", "port": 443, "protocol_str": "quic"}], "anonymization": ["MAAAACsAAABodHRwczovL29wdGltaXphdGlvbmd1aWRlLXBhLmdvb2dsZWFwaXMuY29tAA==", false, 0], "network_stats": {"srtt": 849615}, "server": "https://beacons.gcp.gvt2.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13396353136665437", "port": 443, "protocol_str": "quic"}], "anonymization": ["MAAAACwAAABodHRwczovL3Bhc3N3b3Jkc2xlYWtjaGVjay1wYS5nb29nbGVhcGlzLmNvbQ==", false, 0], "network_stats": {"srtt": 54810}, "server": "https://passwordsleakcheck-pa.googleapis.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "network_stats": {"srtt": 44048}, "server": "https://www.google.com"}], "supports_quic": {"address": "**************", "used_quic": true}, "version": 5}, "network_qualities": {"CAISABiAgICA+P////8B": "4G"}}}