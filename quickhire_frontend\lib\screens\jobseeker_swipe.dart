import 'package:flutter/material.dart';
import '../models/jobseeker_model.dart';
import '../models/job_listing.dart';
import '../utils/job_listing_adapter.dart';
import '../services/job_service.dart';
import '../widgets/bottom_nav_bar.dart';
import '../widgets/jobseeker_card.dart';

class JobSeekerSwipeScreen extends StatefulWidget {
  static const String id = 'JobSeekerSwipeScreen';
  const JobSeekerSwipeScreen({Key? key}) : super(key: key);

  @override
  JobSeekerSwipeScreenState createState() => JobSeekerSwipeScreenState();
}

class JobSeekerSwipeScreenState extends State<JobSeekerSwipeScreen> {
  final JobService _jobService = JobService();
  List<JobListing> _matchedProjects = [];
  bool _isLoading = true;
  int _currentIndex = 0;

  @override
  void initState() {
    super.initState();
    _loadProjects();
  }

  Future<void> _loadProjects() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Get matched projects from the consolidated service
      final jobListings = await _jobService.getMatchedJobListings();
      setState(() {
        _matchedProjects = jobListings;
        _isLoading = false;
      });
    } catch (e) {
      print('Error loading matched job listings: $e');
      // For demo purposes, load mock data if API fails
      final mockListings = await _jobService.getMockJobListings();
      setState(() {
        _matchedProjects = mockListings;
        _isLoading = false;
      });
    }
  }

  void _handleSwipe(bool isLiked) async {
    if (_currentIndex < _matchedProjects.length) {
      final currentJob = _matchedProjects[_currentIndex];
      
      if (isLiked) {
        try {
          // Apply to the job using the consolidated service
          await _jobService.applyToJobListing(currentJob.id);
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Applied to job successfully!')),
          );
        } catch (e) {
          print('Error applying to job: $e');
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Failed to apply to job: $e')),
          );
        }
      }
      
      // Move to the next job
      setState(() {
        _currentIndex++;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[200],
      appBar: AppBar(
        title: const Text('Available Projects'),
        backgroundColor: Colors.white,
        foregroundColor: Colors.amber,
        elevation: 1,
      ),
      body: _isLoading 
        ? const Center(child: CircularProgressIndicator())
        : _matchedProjects.isEmpty 
          ? const Center(child: Text('No matching projects found'))
          : _currentIndex >= _matchedProjects.length
            ? Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.check_circle_outline, size: 80, color: Colors.amber),
                    const SizedBox(height: 20),
                    const Text(
                      'No more jobs right now',
                      style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 10),
                    const Text(
                      'Check back later for new opportunities',
                      style: TextStyle(fontSize: 16, color: Colors.grey),
                    ),
                    const SizedBox(height: 30),
                    ElevatedButton(
                      onPressed: () {
                        // Reload projects
                        _loadProjects();
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.amber,
                        padding: const EdgeInsets.symmetric(horizontal: 30, vertical: 12),
                      ),
                      child: const Text('Refresh', style: TextStyle(fontSize: 16)),
                    ),
                  ],
                ),
              )
            : Column(
                children: [
                  Expanded(
                    child: Stack(
                      children: [
                        // Show next card in stack if available
                        if (_currentIndex < _matchedProjects.length - 1)
                          Positioned.fill(
                            child: Padding(
                              padding: const EdgeInsets.all(20.0),
                              child: _buildProjectCard(
                                _matchedProjects[_currentIndex + 1],
                                (_) {}, // No swipe function for background card
                                key: ValueKey('next-${_matchedProjects[_currentIndex + 1].id}'),
                              ),
                            ),
                          ),

                        // Current card with swipe functionality
                        Positioned.fill(
                          child: Padding(
                            padding: const EdgeInsets.all(20.0),
                            child: _buildProjectCard(
                              _matchedProjects[_currentIndex],
                              _handleSwipe,
                              key: ValueKey(_matchedProjects[_currentIndex].id),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
    );
  }

  Widget _buildProjectCard(JobListing project, Function(bool) onSwipe, {Key? key}) {
    return Dismissible(
      key: key ?? ValueKey(project.id),
      onDismissed: (direction) {
        onSwipe(direction == DismissDirection.endToStart);
      },
      dismissThresholds: const {
        DismissDirection.endToStart: 0.2,
        DismissDirection.startToEnd: 0.2,
      },
      background: Container(
        decoration: BoxDecoration(
          color: Colors.green,
          borderRadius: BorderRadius.circular(12),
        ),
        alignment: Alignment.centerLeft,
        padding: const EdgeInsets.only(left: 20),
        child: const Icon(Icons.check_circle, color: Colors.white, size: 36),
      ),
      secondaryBackground: Container(
        decoration: BoxDecoration(
          color: Colors.red,
          borderRadius: BorderRadius.circular(12),
        ),
        alignment: Alignment.centerRight,
        padding: const EdgeInsets.only(right: 20),
        child: const Icon(Icons.cancel, color: Colors.white, size: 36),
      ),
      child: Card(
        elevation: 4,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                project.title,
                style: const TextStyle(fontSize: 22, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              Text(
                project.employer,
                style: TextStyle(fontSize: 18, color: Colors.grey[700]),
              ),
              const SizedBox(height: 16),
              Text(
                JobListingAdapter.getJobDescription(project),
                style: const TextStyle(fontSize: 16),
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  Icon(Icons.location_on, size: 16, color: Colors.grey[600]),
                  const SizedBox(width: 4),
                  Text(project.location, style: TextStyle(color: Colors.grey[600])),
                ],
              ),
              const SizedBox(height: 8),
              Row(
                children: [
                  Icon(Icons.work, size: 16, color: Colors.grey[600]),
                  const SizedBox(width: 4),
                  Text(JobListingAdapter.getEmploymentType(project), style: TextStyle(color: Colors.grey[600])),
                  const SizedBox(width: 16),
                  Icon(Icons.school, size: 16, color: Colors.grey[600]),
                  const SizedBox(width: 4),
                  Text(JobListingAdapter.getExperienceLevel(project), style: TextStyle(color: Colors.grey[600])),
                ],
              ),
              const Spacer(),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    'Swipe left to reject, right to apply',
                    style: TextStyle(color: Colors.grey[600], fontStyle: FontStyle.italic),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
