import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import '../config/app_config.dart';

class MeetingService {
  static const String baseUrl = AppConfig.baseUrl;

  // Get authentication token
  Future<String?> _getToken() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString('token');
  }

  // Get headers with authentication
  Future<Map<String, String>> _getHeaders() async {
    final token = await _getToken();
    return {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer $token',
    };
  }

  // Create a new meeting
  Future<Map<String, dynamic>> createMeeting({
    required String title,
    String? description,
    required List<String> participantIds,
    String? projectId,
    required DateTime scheduledTime,
    int duration = 60,
    String meetingType = 'video',
    String? meetingLink,
    String? location,
    List<Map<String, dynamic>>? agenda,
  }) async {
    try {
      final headers = await _getHeaders();
      final response = await http.post(
        Uri.parse('$baseUrl/meetings'),
        headers: headers,
        body: jsonEncode({
          'title': title,
          'description': description,
          'participants': participantIds.map((id) => {'user': id}).toList(),
          'project': projectId,
          'scheduledTime': scheduledTime.toIso8601String(),
          'duration': duration,
          'meetingType': meetingType,
          'meetingLink': meetingLink,
          'location': location,
          'agenda': agenda ?? [],
        }),
      );

      final data = jsonDecode(response.body);

      if (response.statusCode == 201) {
        return {
          'success': true,
          'data': data['data'],
        };
      } else {
        return {
          'success': false,
          'message': data['message'] ?? 'Failed to create meeting',
        };
      }
    } catch (e) {
      return {
        'success': false,
        'message': 'Network error: $e',
      };
    }
  }

  // Get all meetings for the user
  Future<Map<String, dynamic>> getMeetings({
    String? status,
    bool? upcoming,
    bool? past,
  }) async {
    try {
      final headers = await _getHeaders();
      
      String queryParams = '';
      List<String> params = [];
      
      if (status != null) params.add('status=$status');
      if (upcoming == true) params.add('upcoming=true');
      if (past == true) params.add('past=true');
      
      if (params.isNotEmpty) {
        queryParams = '?${params.join('&')}';
      }

      final response = await http.get(
        Uri.parse('$baseUrl/meetings$queryParams'),
        headers: headers,
      );

      final data = jsonDecode(response.body);

      if (response.statusCode == 200) {
        return {
          'success': true,
          'data': data['data'],
          'count': data['count'],
        };
      } else {
        return {
          'success': false,
          'message': data['message'] ?? 'Failed to get meetings',
        };
      }
    } catch (e) {
      return {
        'success': false,
        'message': 'Network error: $e',
      };
    }
  }

  // Get a specific meeting
  Future<Map<String, dynamic>> getMeeting(String meetingId) async {
    try {
      final headers = await _getHeaders();
      final response = await http.get(
        Uri.parse('$baseUrl/meetings/$meetingId'),
        headers: headers,
      );

      final data = jsonDecode(response.body);

      if (response.statusCode == 200) {
        return {
          'success': true,
          'data': data['data'],
        };
      } else {
        return {
          'success': false,
          'message': data['message'] ?? 'Failed to get meeting',
        };
      }
    } catch (e) {
      return {
        'success': false,
        'message': 'Network error: $e',
      };
    }
  }

  // Update a meeting
  Future<Map<String, dynamic>> updateMeeting(
    String meetingId,
    Map<String, dynamic> updates,
  ) async {
    try {
      final headers = await _getHeaders();
      final response = await http.put(
        Uri.parse('$baseUrl/meetings/$meetingId'),
        headers: headers,
        body: jsonEncode(updates),
      );

      final data = jsonDecode(response.body);

      if (response.statusCode == 200) {
        return {
          'success': true,
          'data': data['data'],
        };
      } else {
        return {
          'success': false,
          'message': data['message'] ?? 'Failed to update meeting',
        };
      }
    } catch (e) {
      return {
        'success': false,
        'message': 'Network error: $e',
      };
    }
  }

  // Delete a meeting
  Future<Map<String, dynamic>> deleteMeeting(String meetingId) async {
    try {
      final headers = await _getHeaders();
      final response = await http.delete(
        Uri.parse('$baseUrl/meetings/$meetingId'),
        headers: headers,
      );

      final data = jsonDecode(response.body);

      if (response.statusCode == 200) {
        return {
          'success': true,
        };
      } else {
        return {
          'success': false,
          'message': data['message'] ?? 'Failed to delete meeting',
        };
      }
    } catch (e) {
      return {
        'success': false,
        'message': 'Network error: $e',
      };
    }
  }

  // Respond to meeting invitation
  Future<Map<String, dynamic>> respondToMeeting(
    String meetingId,
    String status, // 'accepted' or 'declined'
  ) async {
    try {
      final headers = await _getHeaders();
      final response = await http.put(
        Uri.parse('$baseUrl/meetings/$meetingId/respond'),
        headers: headers,
        body: jsonEncode({'status': status}),
      );

      final data = jsonDecode(response.body);

      if (response.statusCode == 200) {
        return {
          'success': true,
          'data': data['data'],
        };
      } else {
        return {
          'success': false,
          'message': data['message'] ?? 'Failed to respond to meeting',
        };
      }
    } catch (e) {
      return {
        'success': false,
        'message': 'Network error: $e',
      };
    }
  }

  // Start a meeting
  Future<Map<String, dynamic>> startMeeting(String meetingId) async {
    try {
      final headers = await _getHeaders();
      final response = await http.put(
        Uri.parse('$baseUrl/meetings/$meetingId/start'),
        headers: headers,
      );

      final data = jsonDecode(response.body);

      if (response.statusCode == 200) {
        return {
          'success': true,
          'data': data['data'],
        };
      } else {
        return {
          'success': false,
          'message': data['message'] ?? 'Failed to start meeting',
        };
      }
    } catch (e) {
      return {
        'success': false,
        'message': 'Network error: $e',
      };
    }
  }

  // End a meeting
  Future<Map<String, dynamic>> endMeeting(
    String meetingId, {
    String? notes,
    String? recordingUrl,
    int? recordingDuration,
  }) async {
    try {
      final headers = await _getHeaders();
      final response = await http.put(
        Uri.parse('$baseUrl/meetings/$meetingId/end'),
        headers: headers,
        body: jsonEncode({
          'notes': notes,
          'recordingUrl': recordingUrl,
          'recordingDuration': recordingDuration,
        }),
      );

      final data = jsonDecode(response.body);

      if (response.statusCode == 200) {
        return {
          'success': true,
          'data': data['data'],
        };
      } else {
        return {
          'success': false,
          'message': data['message'] ?? 'Failed to end meeting',
        };
      }
    } catch (e) {
      return {
        'success': false,
        'message': 'Network error: $e',
      };
    }
  }
}
