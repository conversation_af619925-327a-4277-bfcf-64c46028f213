// Simple script to get your local IP address for Flutter testing
const os = require('os');

function getLocalIP() {
  const interfaces = os.networkInterfaces();
  
  for (const name of Object.keys(interfaces)) {
    for (const interface of interfaces[name]) {
      // Skip over non-IPv4 and internal (i.e. 127.0.0.1) addresses
      if (interface.family === 'IPv4' && !interface.internal) {
        console.log(`\n🌐 Your local IP address: ${interface.address}`);
        console.log(`\n📱 Update your Flutter AppConfig with:`);
        console.log(`   static const String serverIP = "${interface.address}";`);
        console.log(`\n🔗 Your API will be available at: http://${interface.address}:5000`);
        return interface.address;
      }
    }
  }
  
  console.log('❌ Could not find local IP address');
  return null;
}

console.log('🔍 Finding your local IP address for Flutter testing...');
getLocalIP();
