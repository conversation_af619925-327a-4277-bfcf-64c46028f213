// lib/models/job_listing.dart

class JobListing {
  final String id;
  final String employer;
  final String title;
  final String description;
  final List<String> skills;
  final String location;
  final String workType;
  final double budget;
  final String duration;
  final String status;
  final DateTime createdAt;

  // Additional frontend-specific fields
  final bool isShortlisted;
  final int? matchScore;
  final List<String>? matchedSkills;

  JobListing({
    required this.id,
    required this.employer,
    required this.title,
    required this.description,
    required this.skills,
    required this.location,
    required this.workType,
    required this.budget,
    required this.duration,
    required this.status,
    required this.createdAt,
    this.isShortlisted = false,
    this.matchScore,
    this.matchedSkills,
  });

  factory JobListing.fromJson(Map<String, dynamic> json) {
    return JobListing(
      id: json['_id'] ?? json['id'] ?? '',
      employer: json['employer'] is Map ? json['employer']['name'] ?? '' : json['employer'] ?? '',
      title: json['title'] ?? '',
      description: json['description'] ?? '',
      skills: json['skills'] != null ? List<String>.from(json['skills']) : [],
      location: json['location'] ?? '',
      workType: json['workType'] ?? 'remote',
      budget: (json['budget'] ?? 0).toDouble(),
      duration: json['duration'] ?? '',
      status: json['status'] ?? 'open',
      createdAt: json['createdAt'] != null
          ? DateTime.parse(json['createdAt'])
          : DateTime.now(),
      isShortlisted: json['isShortlisted'] ?? false,
      matchScore: json['matchScore'],
      matchedSkills: json['matchedSkills'] != null ? List<String>.from(json['matchedSkills']) : null,
    );
  }

  // Convert JobListing to JSON for API
  Map<String, dynamic> toJson() {
    return {
      'title': title,
      'description': description,
      'skills': skills,
      'location': location,
      'workType': workType,
      'budget': budget,
      'duration': duration,
      'status': status,
    };
  }
}
