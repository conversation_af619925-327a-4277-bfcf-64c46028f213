// models/JobSeekerProfile.js
const mongoose = require('mongoose');

const JobSeekerProfileSchema = new mongoose.Schema({
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  bio: {
    type: String,
    default: ''
  },
  skills: [{
    type: String
  }],
  phoneNumber: {
    type: String,
    default: ''
  },
  experience: {
    type: String,
    default: ''
  },
  education: {
    type: String,
    default: ''
  },
  portfolio: {
    type: String,
    default: ''
  },
  profilePicture: {
    type: String,
    default: ''
  },
  savedProjects: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Project'
  }],
  createdAt: {
    type: Date,
    default: Date.now
  }
});

module.exports = mongoose.model('JobSeekerProfile', JobSeekerProfileSchema);