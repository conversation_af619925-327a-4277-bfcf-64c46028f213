// Test script for profile and project APIs
const http = require('http');

function makeRequest(path, method = 'GET', data = null, token = null) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: 5001,
      path: path,
      method: method,
      headers: {
        'Content-Type': 'application/json',
      }
    };

    if (token) {
      options.headers['Authorization'] = `Bearer ${token}`;
    }

    const req = http.request(options, (res) => {
      let body = '';
      res.on('data', (chunk) => {
        body += chunk;
      });
      res.on('end', () => {
        try {
          const parsedBody = JSON.parse(body);
          resolve({
            status: res.statusCode,
            body: parsedBody,
            path: path
          });
        } catch (error) {
          resolve({
            status: res.statusCode,
            body: body,
            path: path
          });
        }
      });
    });

    req.on('error', (err) => {
      reject(err);
    });

    if (data) {
      req.write(JSON.stringify(data));
    }
    req.end();
  });
}

async function testAPIs() {
  console.log('🧪 Testing QuickHire Profile & Project APIs...\n');

  // Test basic endpoints
  const basicTests = [
    { path: '/', name: 'Root endpoint' },
    { path: '/api/v1/auth', name: 'Auth endpoint' },
    { path: '/api/v1/profile', name: 'Profile endpoint (should require auth)' },
    { path: '/api/v1/projects', name: 'Projects endpoint' },
    { path: '/api/v1/projects/saved', name: 'Saved projects endpoint (should require auth)' },
  ];

  console.log('📋 Testing Basic Endpoints:');
  for (const test of basicTests) {
    try {
      const result = await makeRequest(test.path);
      const status = result.status === 200 ? '✅' : 
                    result.status === 401 ? '🔒' : 
                    result.status === 404 ? '❌' : '⚠️';
      
      console.log(`${status} ${test.name}: ${result.status} - ${test.path}`);
      
      if (result.status === 200 && test.path === '/') {
        console.log(`   Response: ${result.body}`);
      }
    } catch (error) {
      console.log(`❌ ${test.name}: Connection failed - ${test.path}`);
    }
  }

  console.log('\n📝 Testing Registration:');
  
  // Test user registration
  const testUser = {
    name: 'Test JobSeeker',
    email: '<EMAIL>',
    password: 'password123',
    role: 'jobseeker',
    location: 'New York',
    bio: 'I am a test job seeker',
    skills: ['JavaScript', 'React', 'Node.js']
  };

  try {
    const registerResult = await makeRequest('/api/v1/auth/register', 'POST', testUser);
    console.log(`📝 Registration: ${registerResult.status}`);
    
    if (registerResult.status === 201) {
      console.log(`   ✅ User registered successfully`);
      console.log(`   📧 Check console for OTP (email: ${testUser.email})`);
    } else {
      console.log(`   ❌ Registration failed: ${registerResult.body.message}`);
    }
  } catch (error) {
    console.log(`❌ Registration failed: ${error.message}`);
  }

  console.log('\n📊 Available Endpoints Summary:');
  console.log('🔐 Authentication:');
  console.log('   POST /api/v1/auth/register - Register user');
  console.log('   POST /api/v1/auth/login - Login user');
  console.log('   POST /api/v1/auth/verify-email - Verify email with OTP');
  console.log('   GET  /api/v1/auth/me - Get current user');
  
  console.log('\n👤 Profile Management:');
  console.log('   GET  /api/v1/profile - Get user profile');
  console.log('   PUT  /api/v1/profile - Update user profile');
  console.log('   POST /api/v1/profile/picture - Upload profile picture');
  console.log('   DELETE /api/v1/profile - Delete account');
  
  console.log('\n💼 Project Management:');
  console.log('   GET  /api/v1/projects - Get all projects');
  console.log('   POST /api/v1/projects - Create project (employer)');
  console.log('   GET  /api/v1/projects/matches/find - Get matched projects (jobseeker)');
  console.log('   POST /api/v1/projects/:id/accept - Apply to project (jobseeker)');
  console.log('   POST /api/v1/projects/:id/save - Save/unsave project (jobseeker)');
  console.log('   GET  /api/v1/projects/saved - Get saved projects (jobseeker)');
  console.log('   GET  /api/v1/projects/employer/list - Get employer projects');
  console.log('   GET  /api/v1/projects/jobseeker/accepted - Get accepted projects');

  console.log('\n💬 Communication:');
  console.log('   GET  /api/v1/messages - Get conversations');
  console.log('   POST /api/v1/messages - Send message');
  
  console.log('\n📅 Meetings:');
  console.log('   GET  /api/v1/meetings - Get meetings');
  console.log('   POST /api/v1/meetings - Create meeting');
  
  console.log('\n📞 Video Calls:');
  console.log('   POST /api/v1/calls/initiate - Start video call');
  console.log('   PUT  /api/v1/calls/:id/accept - Accept call');

  console.log('\n🔔 Notifications:');
  console.log('   PUT  /api/v1/notifications/token - Update FCM token');
  console.log('   POST /api/v1/notifications/test - Send test notification');

  console.log('\n📋 Status Code Legend:');
  console.log('   ✅ 200 - OK (working)');
  console.log('   🔒 401 - Unauthorized (needs auth token)');
  console.log('   ❌ 404 - Not Found (check route)');
  console.log('   ⚠️  Other - Check server logs');
  
  console.log('\n💡 Next Steps:');
  console.log('   1. Start Flutter app: flutter run -d web');
  console.log('   2. Register a test account in the app');
  console.log('   3. Test profile editing, job saving, and applying');
  console.log('   4. Check browser DevTools Network tab for API calls');
}

testAPIs().catch(error => {
  console.log('❌ Test failed:', error.message);
});
