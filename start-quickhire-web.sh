#!/bin/bash

echo "🚀 Starting QuickHire for Chrome Web Testing"
echo ""

echo "📁 Checking directories..."
if [ ! -d "fyp_qhbackend" ]; then
    echo "❌ Backend directory not found!"
    echo "Make sure you're in the project root directory."
    exit 1
fi

if [ ! -d "quickhire_frontend" ]; then
    echo "❌ Frontend directory not found!"
    echo "Make sure you're in the project root directory."
    exit 1
fi

echo "✅ Directories found!"
echo ""

echo "🔧 Starting Backend Server..."
cd fyp_qhbackend
osascript -e 'tell app "Terminal" to do script "cd '$(pwd)' && npm start"' 2>/dev/null || gnome-terminal -- bash -c "cd $(pwd) && npm start; exec bash" 2>/dev/null || xterm -e "cd $(pwd) && npm start" &
cd ..

echo "⏳ Waiting for backend to start..."
sleep 3

echo "🌐 Starting Flutter Web App..."
cd quickhire_frontend
osascript -e 'tell app "Terminal" to do script "cd '$(pwd)' && flutter run -d web"' 2>/dev/null || gnome-terminal -- bash -c "cd $(pwd) && flutter run -d web; exec bash" 2>/dev/null || xterm -e "cd $(pwd) && flutter run -d web" &
cd ..

echo ""
echo "🎉 QuickHire is starting!"
echo ""
echo "📋 What's happening:"
echo "   • Backend server starting on http://localhost:5000"
echo "   • Flutter web app starting (will show URL in terminal)"
echo "   • Chrome will open automatically with your app"
echo ""
echo "💡 Tips:"
echo "   • Check both terminal windows for any errors"
echo "   • Backend terminal shows API requests and logs"
echo "   • Frontend terminal shows Flutter build progress"
echo ""
echo "🔍 If something goes wrong:"
echo "   • Make sure Node.js and Flutter are installed"
echo "   • Check if ports 5000 and 3000 are available"
echo "   • Run 'npm install' in fyp_qhbackend if needed"
echo "   • Run 'flutter pub get' in quickhire_frontend if needed"
echo ""
echo "Press any key to continue..."
read -n 1
