@echo off
echo 🚀 Starting QuickHire for Chrome Web Testing
echo.

echo 📁 Checking directories...
if not exist "fyp_qhbackend" (
    echo ❌ Backend directory not found!
    echo Make sure you're in the project root directory.
    pause
    exit /b 1
)

if not exist "quickhire_frontend" (
    echo ❌ Frontend directory not found!
    echo Make sure you're in the project root directory.
    pause
    exit /b 1
)

echo ✅ Directories found!
echo.

echo 🔧 Starting Backend Server...
cd fyp_qhbackend
start "QuickHire Backend" cmd /k "npm start"
cd ..

echo ⏳ Waiting for backend to start...
timeout /t 3 /nobreak >nul

echo 🌐 Starting Flutter Web App...
cd quickhire_frontend
start "QuickHire Frontend" cmd /k "flutter run -d web"
cd ..

echo.
echo 🎉 QuickHire is starting!
echo.
echo 📋 What's happening:
echo    • Backend server starting on http://localhost:5000
echo    • Flutter web app starting (will show URL in terminal)
echo    • Chrome will open automatically with your app
echo.
echo 💡 Tips:
echo    • Check both terminal windows for any errors
echo    • Backend terminal shows API requests and logs
echo    • Frontend terminal shows Flutter build progress
echo.
echo 🔍 If something goes wrong:
echo    • Make sure Node.js and Flutter are installed
echo    • Check if ports 5000 and 3000 are available
echo    • Run 'npm install' in fyp_qhbackend if needed
echo    • Run 'flutter pub get' in quickhire_frontend if needed
echo.
pause
